# Standard library imports
import os
from datetime import datetime

# Third-party imports
from docx import Document
from docx.oxml import parse_xml
from docx.oxml.ns import nsdecls
from docx.shared import Cm, Pt

# Project-specific imports
from src.utils.logger import logger


def export_to_docx(data, output_file, profiler_name):
    """
    Export data to a Word document with custom formatting.

    Args:
        data: List of dictionaries containing the data to export
        output_file: Base name for the output file
        profiler_name: Profiler object containing configuration
    """
    profiler = profiler_name
    logger.info(f"Creating DOCX document with {len(data)} items")

    # Create a new Word document
    doc = Document()
    # Add a centered title with a larger font
    title = doc.add_paragraph()
    title_text = title.add_run(profiler.document_title)
    title_text.font.size = Pt(int(profiler.document_title_size))
    title_text.font.name = profiler.document_title_font
    title_text.bold = True
    title.alignment = 1  # Center alignment

    for data_dict in data:
        # Create a table with two columns
        table = doc.add_table(rows=1, cols=2)
        table.autofit = False
        column_width_ratios = profiler.table_width_ratio  # Ratio of column widths
        total_width_cm = int(profiler.table_total_width) # Total width of the table in cm

        # Set the table column widths based on ratios
        for i, width_ratio in enumerate(column_width_ratios):
            width_cm = total_width_cm * (width_ratio / sum(column_width_ratios))
            table.columns[i].width = Cm(width_cm)

        # Set the table style
        table.style = 'Table Grid'

        # Set the table headers
        header_cells = table.rows[0].cells
        header_cells[0].text = 'SOC Ticket Number'
        header_cells[1].text = '#'

        # Modify the font attributes of the header cells
        for cell in header_cells:
            run = cell.paragraphs[0].runs[0]
            run.font.name = profiler.content_font
            run.font.size = Pt(int(profiler.content_size))
            run.font.bold = True

        # Add data to the table
        for key, value in data_dict.items():
            row_cells = table.add_row().cells
            row_cells[0].text = str(key)
            row_cells[1].text = str(value)

            # Apply bold style to the first column
            row_cells[0].paragraphs[0].runs[0].bold = True

            # Change the font name and size
            row_cells[0].paragraphs[0].runs[0].font.name = profiler.content_font
            row_cells[0].paragraphs[0].runs[0].font.size = Pt(int(profiler.content_size))
            row_cells[1].paragraphs[0].runs[0].font.name = profiler.content_font
            row_cells[1].paragraphs[0].runs[0].font.size = Pt(int(profiler.content_size))

        # Color the background of the first column
        for row in table.rows:
            cell = row.cells[0]
            cell._element.get_or_add_tcPr().append(
                parse_xml('<w:shd {} w:fill="4c999c"/>'.format(nsdecls('w')))
            )

        # Add an empty paragraph after the table
        doc.add_paragraph()

    # Reformat file name and save it
    current_datetime = datetime.now()
    formatted_datetime = current_datetime.strftime("%Y-%m-%d_%H-%M-%S")
    output_file = output_file + formatted_datetime + ".docx"
    file_path = os.path.join(profiler.directory, output_file)

    # Save the document
    doc.save(file_path)
    logger.info(f"DOCX document saved to {file_path}")