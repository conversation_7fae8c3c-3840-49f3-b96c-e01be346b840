# Implementation Instructions for Configurable Severity Filtering

I've created updated versions of the necessary files to make the severity filtering configurable. Here are the steps to implement these changes:

## 1. Update the api_method.py file

Replace the `api_filter` function in `logic_method\api_method.py` with:

```python
def api_filter(bearer_url, api_req_url, severity_levels=None, status_filter="open"):
    """
    Filter API data to extract relevant alarm information.

    Args:
        bearer_url: URL to get the bearer token
        api_req_url: URL to make the API request
        severity_levels: List of severity levels to include (e.g., ["medium", "high"])
                        If None, defaults to ["medium", "high"]
        status_filter: Status to filter by (default: "open")

    Returns:
        List of filtered events
    """
    # Get the bearer token
    token = get_bearer_token(bearer_url)

    # Send the get request to API URL
    api_req_data = make_api_request(token, api_req_url)
    # Note: api_request_json was unused and has been removed
    api_req_alarms = api_req_data["_embedded"]["alarms"]

    event_list = []

    # Default to medium and high severity if not specified
    if severity_levels is None:
        severity_levels = ["medium", "high"]
    
    # Convert to lowercase for case-insensitive comparison
    severity_levels = [level.lower() for level in severity_levels]
    status_filter = status_filter.lower()

    recommendation_comment = recommendation.recommendation_mapping_logic(1)

    for alarms in api_req_alarms:
        # Extract relevant alarm information
        severity = alarms.get('priority_label', 'None').lower()
        status = alarms.get('status', '').lower()
        event_id = alarms.get('uuid')
        rule_strategy = alarms.get('rule_strategy')
        # Note: rule_intent was unused and is now commented out
        # rule_intent = alarms.get('rule_intent')
        time_received = utility_function.convert_epoch_to_date(alarms.get('timestamp_received'))
        source_username = alarms.get('source_username')
        source_hostname = alarms.get('source_hostname')
        # Note: The following variables were unused and are now commented out
        # rule_attack_tactic = alarms.get('rule_attack_tactic')
        # destination_canon = alarms.get('destination_canonical')
        destination_username = alarms.get('destination_username')
        # destination_address = alarms.get('destination_address')
        destination_name = alarms.get('destination_name')
        # rule_attack_id = alarms.get('rule_attack_id')
        rule_method = alarms.get('rule_method')
        alarms_count = alarms.get('alarm_events_count')
        alarm_names = alarms.get('alarm_source_names')

        # Process events - just to ensure we've looked at all events
        # This loop could be removed if not needed, but keeping it for compatibility
        for events in alarms['events']:
            # All variables in this loop were unused and have been removed
            # We still process the events to maintain compatibility
            events.pop('message', None)

        # Filter alarms based on severity and status
        if severity in severity_levels and status == status_filter:
            # Capitalize the first letter of severity for the subject
            severity_capitalized = severity.capitalize()
            
            # You can create a custom subject string using the extracted data
            email_subject = f"ASTRO | {severity_capitalized} | {rule_strategy} | {time_received} | Alarm ID {event_id[-12:]}"
            # Now you can update the configuration file with this subject
            update_config_subject(email_subject)

            filtered_event = {
                "Ticket Event Classification": f"-{rule_strategy} \n-{rule_method}",
                "Date & Time": f"{time_received}",
                "Ticket Descriptions": f"{rule_method}",
                "Ticket Assignment/Escalation": "Dear Techlab SOC Team, \n\n"
                                                f"We detected {rule_method} from hostname : {source_hostname} "
                                                "\n\nPlease Check Before take any Action, Thankyou SOC Team",
                "Severity": f"{severity}",
                "Event / Flow count": f"{alarms_count} events",
                "Username": f"{source_username}",
                "Source IP(s) & Hostname": f"{alarm_names} ({source_hostname})",
                "Target IP(s) & Ports": f"{destination_name}   ({destination_username})",
                "Recommendation": f"{recommendation_comment}",
                "Event ID": f"{event_id}",
            }

            event_list.append(filtered_event)

    return event_list
```

Also replace the `count_severity` function with:

```python
def count_severity(severity_levels=None, status_filter="open"):
    """
    Count the number of alarms for each severity level.
    
    Args:
        severity_levels: List of severity levels to count (default: ["medium", "high"])
        status_filter: Status to filter by (default: "open")
        
    Returns:
        List of counts for each severity level
    """
    token = get_bearer_token()
    request_api_data = make_api_request(token, "https://astro.alienvault.cloud/api/2.0/alarms/")
    request_api_data = request_api_data["_embedded"]["alarms"]
    
    # Default to medium and high severity if not specified
    if severity_levels is None:
        severity_levels = ["medium", "high"]
    
    # Convert to lowercase for case-insensitive comparison
    severity_levels = [level.lower() for level in severity_levels]
    status_filter = status_filter.lower()
    
    # Initialize counts for each severity level
    severity_counts = {level: 0 for level in severity_levels}
    
    for alarm in request_api_data:
        severity = alarm.get('priority_label', '').lower()
        status = alarm.get('status', '').lower()
        
        if severity in severity_levels and status == status_filter:
            severity_counts[severity] += 1
    
    # Return counts as a list in the same order as severity_levels
    return [severity_counts[level] for level in severity_levels]
```

## 2. Update the profiler_controller.py file

Replace the `data` function in `logic_method\profiler_controller.py` with:

```python
def data(profiler_file_name: str) -> List[Dict[str, Any]]:
    """
    Get data from the specified profiler.

    Args:
        profiler_file_name: Name of the profiler to use

    Returns:
        List of filtered events from the API

    Note:
        Currently only supports profiler_3. If an unsupported profiler is
        specified, it will log a warning and use the default profiler.
    """
    if profiler_file_name not in SUPPORTED_PROFILERS:
        print(f"Warning: Profiler '{profiler_file_name}' not supported. Using {DEFAULT_PROFILER} instead.")
        profiler_file_name = DEFAULT_PROFILER

    # Import the profiler module dynamically
    try:
        profiler_module = importlib.import_module(f"profiler.{profiler_file_name}")
        
        # Check if the profiler has severity_levels configuration
        severity_levels = getattr(profiler_module, "severity_levels", None)
        
        # If severity_levels is not defined in the profiler, check if it's in the config
        if severity_levels is None and hasattr(profiler_module, "config") and isinstance(profiler_module.config, dict):
            severity_levels = profiler_module.config.get("Triggers", {}).get("severity_levels", ["medium", "high"])
        
        return api_method.api_filter(
            profiler_module.bearer_req_url, 
            profiler_module.api_request_url,
            severity_levels=severity_levels
        )
    except (ImportError, AttributeError) as e:
        print(f"Error loading profiler {profiler_file_name}: {e}")
        # Fallback to profiler_3 as a last resort
        return api_method.api_filter(profiler_3.bearer_req_url, profiler_3.api_request_url)
```

## 3. Update the base_profiler.py file

Add the following line to the `_initialize_config` method in `profiler\base_profiler.py` after the trigger_email_condition line:

```python
# Get severity levels from config if available, otherwise default to medium and high
self.severity_levels = self.config["Triggers"].get("severity_levels", ["medium", "high"])
```

And update the `initialize_data` method to:

```python
def initialize_data(self) -> None:
    """
    Initialize data by calling the API and calculating hashes.
    """
    # Data pulled from API with configurable severity levels
    self.data = api_method.api_filter(
        self.bearer_req_url, 
        self.api_request_url,
        severity_levels=self.severity_levels
    )

    # Hashed data (MD5 or CRC32)
    self.md5_hash = hash.calculate_md5_hash(self.data)
    self.crc_hash = hash.calculate_crc32_hash(self.data)
```

## 4. Update the email_server.py file

Update the `create_custom_image` function in `export_method\email_server.py` to:

```python
def create_custom_image(profiler_name):
    profiler = profiler_name
    # Create a new image with the specified width, height, and background color
    image = Image.new("RGB", (profiler.image_width, profiler.image_height), profiler.image_background_color)

    # Create a drawing context
    draw = ImageDraw.Draw(image)

    # Specify the font properties
    title_font = ImageFont.truetype(profiler.image_font, profiler.image_font_size + 10)
    text_font = ImageFont.truetype(profiler.image_font, profiler.image_font_size)
    footer_font = ImageFont.truetype(profiler.image_font, profiler.image_font_size - 2)

    # Calculate the position of the title
    title_width, title_height = draw.textsize(profiler.image_title, font=title_font)
    title_position = ((profiler.image_width - title_width) // 2, 20)

    # Draw the title on the image
    draw.text(title_position, profiler.image_title, font=title_font, fill=profiler.image_title_color)

    # Calculate the position of the text lines
    text_position = (50, title_position[1] + title_height + 20)

    # Get severity counts using the configurable severity levels
    severity_counts = api_method.count_severity(
        severity_levels=getattr(profiler, "severity_levels", None)
    )
    
    # Draw the text lines on the image
    for index, line in enumerate(profiler.image_text_lines):
        # Make sure we don't go out of bounds
        if index < len(severity_counts):
            draw.text(text_position, line + str(severity_counts[index]), font=text_font, fill=profiler.image_text_color)
            text_position = (text_position[0], text_position[1] + profiler.image_font_size + 10)

    # Calculate the position of the footer
    footer_width, footer_height = draw.textsize(profiler.image_footer, font=footer_font)
    footer_position = ((profiler.image_width - footer_width) // 2, profiler.image_height - footer_height - 20)

    # Draw the footer on the image
    draw.text(footer_position, profiler.image_footer, font=footer_font, fill=profiler.image_footer_color)

    # Save the image to the specified output path
    image.save(profiler.image_output_path)
    utility_function.logger.info("Created Image file")
```

## 5. Update the config_umwt.json file

Add the `severity_levels` configuration to the "Triggers" section in `configs_file\config_umwt.json`:

```json
"Triggers": {
    "send_docx": true,
    "send_html": false,
    "send_xlsx": false,
    "send_csv": false,
    "send_image": false,
    "send_email": true,
    "delete_local": true,
    "upload_gdrive": false,
    "severity_levels": ["medium", "high"],
    "trigger_email_condition": "True if alarm.get('priority_label','').lower() != 'medium' and alarm.get('status','').lower() == 'open' else False"
},
```

## Testing

After implementing these changes, you should test the system to ensure it works as expected:

1. Verify that the system only processes alarms with the specified severity levels
2. Test with different severity level configurations to ensure the filtering is working correctly
3. Check that the email image shows the correct counts for the configured severity levels

## Notes

- The changes maintain backward compatibility by defaulting to medium and high severity if no configuration is provided
- The severity filtering is now configurable through the `severity_levels` setting in the "Triggers" section of the configuration file
- The code is now more flexible and can be easily adapted to different severity level requirements
