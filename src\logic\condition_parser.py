# Standard library imports
import re
from typing import Dict, Any

def safe_evaluate_condition(condition_str: str, alarm_data: Dict[str, Any]) -> bool:
    """
    Safely evaluates a condition string against alarm data without using eval().

    Args:
        condition_str: A string containing the condition to evaluate
        alarm_data: Dictionary containing the alarm data to check against

    Returns:
        bool: Result of the condition evaluation
    """
    # Parse the condition string to extract the logic
    # Expected format: "True if <condition> else False"
    match = re.match(r'True\s+if\s+(.*?)\s+else\s+False', condition_str)
    if not match:
        print(f"Warning: Condition string '{condition_str}' does not match expected format")
        return False

    condition = match.group(1)

    # Handle common comparison patterns
    return evaluate_complex_condition(condition, alarm_data)

def evaluate_complex_condition(condition: str, alarm_data: Dict[str, Any]) -> bool:
    """
    Evaluates a complex condition with AND/OR logic.

    Args:
        condition: The condition string to evaluate
        alarm_data: Dictionary containing the alarm data

    Returns:
        bool: Result of the condition evaluation
    """
    # Handle AND conditions
    if ' and ' in condition:
        parts = condition.split(' and ')
        return all(evaluate_simple_condition(part.strip(), alarm_data) for part in parts)

    # Handle OR conditions
    if ' or ' in condition:
        parts = condition.split(' or ')
        return any(evaluate_simple_condition(part.strip(), alarm_data) for part in parts)

    # If no AND/OR, it's a simple condition
    return evaluate_simple_condition(condition, alarm_data)

def evaluate_simple_condition(condition: str, alarm_data: Dict[str, Any]) -> bool:
    """
    Evaluates a simple condition without AND/OR logic.

    Args:
        condition: The simple condition string to evaluate
        alarm_data: Dictionary containing the alarm data

    Returns:
        bool: Result of the condition evaluation
    """
    # Pattern 1: alarm.get('field','').lower() == 'value'
    get_pattern = r"alarm\.get\('([^']+)',\s*'([^']*)'\)\.lower\(\)\s*([!=]=)\s*'([^']+)'"
    match = re.match(get_pattern, condition)

    if match:
        field, default, operator, value = match.groups()
        actual_value = alarm_data.get(field, default)

        if actual_value is None:
            actual_value = default

        if isinstance(actual_value, str):
            actual_value = actual_value.lower()

        if operator == '==':
            return str(actual_value) == value
        elif operator == '!=':
            return str(actual_value) != value

    # Pattern 2: alarm.get('field','').lower() in ['value1', 'value2', ...]
    in_pattern = r"alarm\.get\('([^']+)',\s*'([^']*)'\)\.lower\(\)\s+in\s+\[([^\]]+)\]"
    match = re.match(in_pattern, condition)

    if match:
        field, default, values_str = match.groups()
        actual_value = alarm_data.get(field, default)

        if actual_value is None:
            actual_value = default

        if isinstance(actual_value, str):
            actual_value = actual_value.lower()

        # Parse the values list
        values = [v.strip().strip("'\"") for v in values_str.split(',')]
        return str(actual_value) in values

    # Pattern 3: 'value' in alarm.get('field','').lower()
    contains_pattern = r"'([^']+)'\s+in\s+alarm\.get\('([^']+)',\s*'([^']*)'\)\.lower\(\)"
    match = re.match(contains_pattern, condition)

    if match:
        value, field, default = match.groups()
        actual_value = alarm_data.get(field, default)

        if actual_value is None:
            actual_value = default

        if isinstance(actual_value, str):
            actual_value = actual_value.lower()
            return value in actual_value

        return False

    # Pattern 4: True (always true condition)
    if condition.strip() == 'True':
        return True

    # Pattern 5: False (always false condition)
    if condition.strip() == 'False':
        return False

    # If no pattern matches, log a warning and return False
    print(f"Warning: Could not parse condition: {condition}")
    return False
