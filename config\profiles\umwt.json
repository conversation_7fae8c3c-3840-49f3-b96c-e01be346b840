{"Profile": {"name": "UMWT", "description": "UMWT Security Monitoring Profile"}, "API_Credentials": {"api_username": "${ASTRO_API_USERNAME}", "api_secret": "${ASTRO_API_SECRET}"}, "API_URL": {"bearer_request_url": "https://astro.alienvault.cloud/api/2.0/oauth/token", "api_request_url": "https://astro.alienvault.cloud/api/2.0/alarms/"}, "SMTP": {"server": "smtp.gmail.com", "port": 587, "email": "${ASTRO_SMTP_EMAIL}", "app_pass": "${ASTRO_SMTP_PASSWORD}"}, "Email": {"sender": "${ASTRO_SMTP_EMAIL}", "receivers": ["${ASTRO_EMAIL_RECIPIENT}", "${ASTRO_ADDITIONAL_RECIPIENTS}"], "subject": "Security Alert: UMWT Monitoring", "message": "Please see the information below.", "use_template_in_body": true, "attach_files": false, "use_gemini_ai": true}, "Email_Image": {"image_width": 400, "image_height": 300, "image_background_color": [30, 30, 30], "image_title_color": [0, 122, 204], "image_text_color": [220, 220, 220], "image_footer_color": [147, 161, 161], "image_font": "verdana.ttf", "image_font_size": 16, "image_title": "Ticket Summary", "image_text_lines": ["Low Severity Count:", "Medium Severity Count:", "High Severity Count:"], "image_footer": "Techlab Sdn Bhd", "image_output_path": "resources/exports/image.png"}, "Attachments": {"file1": "resources/exports/Astro_Report.xlsx", "file2": "resources/exports/Astro_Report.csv", "file3": "resources/exports/Astro_Report.docx"}, "Directory": {"directory": "resources/exports", "hash_directory": "resources/hash"}, "Docx": {"table_width_ratio": [2, 4], "table_total_width": 15, "document_title": "Ticket Description", "document_title_font": "<PERSON><PERSON>", "document_title_size": 24, "content_font": "<PERSON><PERSON>", "content_size": 11}, "Triggers": {"send_docx": true, "send_html": false, "send_xlsx": false, "send_csv": false, "send_image": false, "send_email": true, "delete_local": true, "upload_gdrive": false, "severity_levels": ["low", "medium", "high"], "trigger_email_condition": "True if alarm.get('status','').lower() == 'open' and alarm.get('priority_label','').lower() in ['low', 'medium', 'high'] else False"}, "Google_Drive_API": {"gdrive_client_secret": "${ASTRO_GDRIVE_CLIENT_SECRET}", "gdrive_directory": "1U__4BpjelLxVKvfZ_7sHZiTltDkKbuVr", "gdrive_api_key": "${ASTRO_GDRIVE_API_KEY}", "gdrive_upload_url": "https://www.googleapis.com/upload/drive/v3/files?uploadType=media&key=", "gdrive_client_config_backend": "file", "gdrive_save_credentials": true, "gdrive_save_credentials_backend": "file", "gdrive_save_credentials_file": "credentials.txt", "gdrive_get_refresh_token": true, "gdrive_client_id": "${ASTRO_GDRIVE_CLIENT_ID}", "gdrive_oauth_scope": "https://www.googleapis.com/auth/drive"}}