# Astro Automation Configuration

This directory contains the configuration files for the Astro Automation application. The configuration is organized into several JSON files for different aspects of the application.

## Configuration Structure

The configuration is organized into the following files:

- `app.json`: Main application settings
- `email.json`: Email server and template settings
- `logging.json`: Logging settings
- `api.json`: API connection settings
- `gdrive.json`: Google Drive settings
- `profiles/*.json`: Profile-specific configurations

## Environment Variables

The configuration files support environment variable substitution using the `${VARIABLE_NAME}` syntax. The following environment variables are used:

- `ASTRO_API_USERNAME`: API username for authentication
- `ASTRO_API_SECRET`: API secret for authentication
- `ASTRO_SMTP_EMAIL`: Email address for sending notifications
- `ASTRO_SMTP_PASSWORD`: Password or app password for the email account
- `ASTRO_EMAIL_RECIPIENT`: Default recipient for email notifications
- `ASTRO_GDRIVE_CLIENT_ID`: Google Drive client ID
- `ASTRO_GDRIVE_CLIENT_SECRET`: Google Drive client secret
- `ASTRO_GDRIVE_API_KEY`: Google Drive API key
- `GEMINI_API_KEY`: Gemini AI API key
- `ASTRO_DEBUG_MODE`: Enable or disable debug mode

## Using the Configuration System

To access the configuration in your code, use the `src.utils.config` module:

```python
from src.utils.config import get_config, get_profile

# Get the application configuration
app_config = get_config('app')

# Get a specific value from the application configuration
polling_interval = get_config('app', 'Service.polling_interval', 180)

# Get a profile configuration
profile_config = get_profile('umwt')
```

## Adding a New Profile

To add a new profile:

1. Copy the `templates/profile.template.json` file to `profiles/your_profile_name.json`
2. Edit the file to customize the settings for your profile
3. Make sure to update the `Profile.name` and `Profile.description` fields

## Templates

The `templates` directory contains template files with documentation for each configuration option:

- `app.template.json`: Template for the main application configuration
- `profile.template.json`: Template for profile-specific configurations
- `README.md`: Documentation for the configuration system

## Environment Variable Setup

Create a `.env` file in the root directory with the following content:

```
# API Credentials
ASTRO_API_USERNAME=your_api_username
ASTRO_API_SECRET=your_api_secret

# SMTP Email Settings
ASTRO_SMTP_EMAIL=<EMAIL>
ASTRO_SMTP_PASSWORD=your_app_password
ASTRO_EMAIL_RECIPIENT=<EMAIL>

# Google Drive API
ASTRO_GDRIVE_CLIENT_ID=your_gdrive_client_id
ASTRO_GDRIVE_CLIENT_SECRET=your_gdrive_client_secret
ASTRO_GDRIVE_API_KEY=your_gdrive_api_key

# Gemini AI API
GEMINI_API_KEY=your_gemini_api_key

# Debug Mode
ASTRO_DEBUG_MODE=false
```
