"""
Test script for the HTML recommendation formatting.
This script tests the HTML formatting of Gemini AI recommendations.
"""
import os
import sys
import json
from datetime import datetime

# Add the project root to the Python path to allow importing project modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment variables from .env file
from src.utils.env_loader import load_env_file
load_env_file()

# Import the necessary modules
from src.export.email_server import create_html_email_body
from src.logic.recommendation import get_batch_gemini_recommendation, get_gemini_recommendation

# Create a mock profiler class for testing
class MockProfiler:
    """Mock profiler class for testing."""
    def __init__(self):
        self.document_title = "Security Alarm Report"
        self.config = {
            "Email": {
                "use_gemini_ai": True
            }
        }

# Create mock alarm data for testing
mock_alarms = [
    {
        "Ticket Event Classification": "Brute Force Authentication",
        "Date & Time": "Sun, May 04 2025, 04:22:55 PM",
        "Ticket Descriptions": "Brute Force Authentication",
        "Ticket Assignment/Escalation": "Dear Techlab Automation Team, \n\nWe detected Brute Force Authentication from hostname : WORKSTATION1 \n\nPlease Check Before take any Action, Thankyou Automation Team",
        "Severity": "low",
        "Event / Flow count": "5 events",
        "Username": "user123",
        "Source IP(s) & Hostname": "************* (WORKSTATION1)",
        "Target IP(s) & Ports": "******** (AUTH-SERVER)",
        "Recommendation": "Default recommendation text",
        "Event ID": "abc123",
        "Email Subject": "ASTRO | Low | Brute Force Authentication | Sun, May 04 2025, 04:22:55 PM | Alarm ID abc123"
    },
    {
        "Ticket Event Classification": "Multiple login failures",
        "Date & Time": "Sun, May 04 2025, 04:20:30 PM",
        "Ticket Descriptions": "Multiple login failures",
        "Ticket Assignment/Escalation": "Dear Techlab Automation Team, \n\nWe detected Multiple login failures from hostname : SERVER2 \n\nPlease Check Before take any Action, Thankyou Automation Team",
        "Severity": "medium",
        "Event / Flow count": "3 events",
        "Username": "admin456",
        "Source IP(s) & Hostname": "************* (SERVER2)",
        "Target IP(s) & Ports": "******** (DATABASE-SERVER)",
        "Recommendation": "Default recommendation text",
        "Event ID": "def456",
        "Email Subject": "ASTRO | Medium | Multiple login failures | Sun, May 04 2025, 04:20:30 PM | Alarm ID def456"
    }
]

# Test the single recommendation formatting
def test_single_recommendation():
    """Test the single recommendation formatting."""
    print("Testing single recommendation formatting...")

    # Get a recommendation for a specific context
    recommendation = get_gemini_recommendation("User reported suspicious login attempts")

    # Save the recommendation to a file for inspection
    with open("single_recommendation_test.html", "w") as f:
        f.write(recommendation if recommendation else "Failed to get recommendation")

    print(f"\nSingle recommendation saved to single_recommendation_test.html")
    print("Open this file in a web browser to see how the recommendation will look.")

# Test the batch recommendation formatting
def test_batch_recommendation():
    """Test the batch recommendation formatting."""
    print("Testing batch recommendation formatting...")

    # Get a batch recommendation for all alarms
    recommendation = get_batch_gemini_recommendation(mock_alarms)

    # Save the recommendation to a file for inspection
    with open("batch_recommendation_test.html", "w") as f:
        f.write(recommendation if recommendation else "Failed to get recommendation")

    print(f"\nBatch recommendation saved to batch_recommendation_test.html")
    print("Open this file in a web browser to see how the recommendation will look.")

# Test the email template with HTML recommendation
def test_email_with_html_recommendation():
    """Test the email template with HTML recommendation."""
    print("Testing email template with HTML recommendation...")

    # Create a mock profiler
    profiler = MockProfiler()

    # Create the HTML email body
    html_content = create_html_email_body(mock_alarms, profiler)

    # Save the HTML content to a file for inspection
    with open("email_with_html_recommendation.html", "w") as f:
        f.write(html_content)

    print(f"\nHTML email content saved to email_with_html_recommendation.html")
    print("Open this file in a web browser to see how the email will look.")

if __name__ == "__main__":
    test_single_recommendation()
    test_batch_recommendation()
    test_email_with_html_recommendation()
