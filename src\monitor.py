"""
Real-time Monitoring Script for Astro Automation

This script provides continuous monitoring for Astro security alarms without
requiring installation as a Windows service. It runs in a loop, checking for
alarms at regular intervals.

Usage:
    python monitor.py [--interval SECONDS] [--debug]

Options:
    --interval SECONDS    Polling interval in seconds (default: 300)
    --debug               Enable debug mode
    --once                Run once and exit (for testing)
"""

import os
import sys
import time
import signal
import argparse
import traceback
from datetime import datetime

# Import the main application modules
from src.utils.env_loader import load_env_file
from src.utils.logger import logger, info, debug
from src.utils.config_loader import get_app_config, get_logging_config
from src.logic import profiler_controller
import src.main as astro_main

# Default polling interval in seconds
DEFAULT_POLLING_INTERVAL = 180  # 3 minutes

# Global flag to control the main loop
running = True


def signal_handler(sig, frame):
    """Handle termination signals gracefully."""
    global running
    print("\nShutdown signal received. Exiting gracefully...")
    running = False


def setup_argument_parser():
    """Set up command line argument parser."""
    parser = argparse.ArgumentParser(description="Real-time monitoring for Astro Automation")
    parser.add_argument(
        "--interval",
        type=int,
        default=DEFAULT_POLLING_INTERVAL,
        help=f"Polling interval in seconds (default: {DEFAULT_POLLING_INTERVAL})"
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug mode"
    )
    parser.add_argument(
        "--once",
        action="store_true",
        help="Run once and exit (for testing)"
    )
    return parser


def load_configuration():
    """Load application configuration."""
    # Load environment variables
    load_env_file()

    # Load application configuration
    app_config = get_app_config()
    logging_config = get_logging_config()

    # Get polling interval from config or use default
    polling_interval = app_config.get("Service", {}).get(
        "polling_interval", DEFAULT_POLLING_INTERVAL
    )

    return app_config, logging_config, polling_interval


def run_monitoring_cycle():
    """Run a single monitoring cycle."""
    # Get the list of profilers to process
    profiler_list = profiler_controller.profiler_list

    # Process each profiler
    results = []
    for profile in profiler_list:
        info(f"Processing profile: {profile}")
        try:
            # Process the profiler
            result = astro_main.process_profiler(profile)
            results.append(result)
            if result:
                info(f"Successfully processed profile {profile}")
            else:
                info(f"No alarms detected for profile {profile}")

        except Exception as e:
            logger.error(f"Error processing profile {profile}: {str(e)}")
            logger.error(traceback.format_exc())

    # Manage log files
    try:
        astro_main.manage_log_files()
    except Exception as e:
        logger.error(f"Error managing log files: {str(e)}")
        logger.error(traceback.format_exc())

    return any(results)


def main():
    """Main function for the monitoring script."""
    # Set up signal handlers for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # Parse command line arguments
    parser = setup_argument_parser()
    args = parser.parse_args()

    # Set debug mode if requested
    if args.debug:
        os.environ["ASTRO_DEBUG_MODE"] = "true"

    # Load configuration
    app_config, logging_config, config_interval = load_configuration()

    # Use command line interval if provided, otherwise use config
    polling_interval = args.interval if args.interval != DEFAULT_POLLING_INTERVAL else config_interval

    print(f"Starting Astro Automation real-time monitoring")
    print(f"Polling interval: {polling_interval} seconds")
    print(f"Debug mode: {'Enabled' if args.debug else 'Disabled'}")
    print(f"Press Ctrl+C to exit")

    # Run once if requested
    if args.once:
        print("Running single monitoring cycle (--once flag specified)")
        run_monitoring_cycle()
        return

    # Main monitoring loop
    consecutive_errors = 0
    max_consecutive_errors = 5

    while running:
        try:
            # Log start of monitoring cycle
            current_time = datetime.now()
            print(f"\n[{current_time}] Starting monitoring cycle")

            # Run the monitoring cycle
            success = run_monitoring_cycle()

            # Reset consecutive errors counter on successful run
            consecutive_errors = 0

            # Log end of monitoring cycle
            end_time = datetime.now()
            duration = (end_time - current_time).total_seconds()
            print(f"[{end_time}] Monitoring cycle completed in {duration:.2f} seconds")
            print(f"Waiting {polling_interval} seconds until next check...")

            # Wait for the next polling interval, checking for termination
            wait_start = time.time()
            while running and (time.time() - wait_start) < polling_interval:
                time.sleep(1)

        except Exception as e:
            consecutive_errors += 1
            print(f"Error in monitoring cycle: {str(e)}")
            logger.error(f"Error in monitoring cycle: {str(e)}")
            logger.error(traceback.format_exc())

            print(f"Consecutive errors: {consecutive_errors}/{max_consecutive_errors}")

            if consecutive_errors >= max_consecutive_errors:
                print(f"Maximum consecutive errors ({max_consecutive_errors}) reached. Resetting...")
                # Reset error counter
                consecutive_errors = 0
                # Wait a bit before continuing
                time.sleep(60)

            # Wait before retrying after an error
            time.sleep(30)

    print("Monitoring stopped")


if __name__ == "__main__":
    main()
