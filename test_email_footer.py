#!/usr/bin/env python3
"""
Test script to verify the email footer changes from "Techlab SOC Team" to "Techlab Automation Team"
"""
import os
import sys

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.export.email_server import create_html_email_body

# Create a mock profiler object
class MockProfiler:
    def __init__(self):
        self.document_title = "Test Security Alert"
        self.config = {
            "Templates": {
                "email_template": "email_template.html",
                "template_dir": "resources/templates"
            }
        }

# Create mock alarm data
mock_alarms = [
    {
        "Ticket Event Classification": "Test Alert",
        "Date & Time": "Fri, Jul 04 2025, 01:00:00 PM",
        "Ticket Descriptions": "Test Alert Description",
        "Ticket Assignment/Escalation": "Dear Techlab Automation Team, \n\nThis is a test alert \n\nPlease Check Before take any Action, Thankyou Automation Team",
        "Severity": "medium",
        "Event / Flow count": "1 events",
        "Username": "testuser",
        "Source IP(s) & Hostname": "************* (test-workstation)",
        "Target IP(s) & Ports": "******** (test-server)",
        "Recommendation": "This is a test recommendation",
        "Event ID": "test123",
        "Email Subject": "Test Alert Email"
    }
]

def test_email_footer():
    """Test that the email footer contains 'Techlab Automation Team' instead of 'Techlab SOC Team'"""
    try:
        # Create mock profiler
        profiler = MockProfiler()
        
        # Generate HTML email body
        html_content = create_html_email_body(profiler, mock_alarms, use_template_in_body=True)
        
        # Check if the content contains the new footer text
        if "Techlab Automation Team" in html_content:
            print("✅ SUCCESS: Email footer correctly shows 'Techlab Automation Team'")
        else:
            print("❌ FAILED: Email footer does not contain 'Techlab Automation Team'")
            
        # Check if the old text is still present (should not be)
        if "Techlab SOC Team" in html_content:
            print("❌ FAILED: Email footer still contains old text 'Techlab SOC Team'")
        else:
            print("✅ SUCCESS: Old text 'Techlab SOC Team' has been removed")
            
        # Save the HTML content to a file for inspection
        with open("test_email_output.html", "w", encoding="utf-8") as f:
            f.write(html_content)
        print("📄 Email HTML content saved to 'test_email_output.html' for inspection")
        
        return html_content
        
    except Exception as e:
        print(f"❌ ERROR: Failed to generate email content: {e}")
        return None

if __name__ == "__main__":
    print("Testing email footer changes...")
    print("=" * 50)
    test_email_footer()
