"""
Test script for the email template with Gemini AI recommendation.
This script tests the integration of Gemini AI recommendations into the email template.
"""
import os
import sys
import json
from datetime import datetime

# Add the project root to the Python path to allow importing project modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment variables from .env file
from src.utils.env_loader import load_env_file
load_env_file()

# Import the necessary modules
from src.export.email_server import create_html_email_body
from src.logic.recommendation import get_batch_gemini_recommendation

# Create a mock profiler object
class MockProfiler:
    def __init__(self):
        self.document_title = "Security Alarm Report"
        self.config = {
            "Email": {
                "use_gemini_ai": True
            }
        }

# Create some mock alarm data for testing
mock_alarms = [
    {
        "Ticket Event Classification": "Brute Force Authentication",
        "Date & Time": "Sun, May 04 2025, 04:22:55 PM",
        "Ticket Descriptions": "Brute Force Authentication",
        "Ticket Assignment/Escalation": "Dear Techlab Automation Team, \n\nWe detected Brute Force Authentication from hostname : workstation1.example.com \n\nPlease Check Before take any Action, Thankyou Automation Team",
        "Severity": "low",
        "Event / Flow count": "5 events",
        "Username": "user123",
        "Source IP(s) & Hostname": "************* (workstation1.example.com)",
        "Target IP(s) & Ports": "******** (server.example.com)",
        "Recommendation": "Default recommendation text",
        "Event ID": "abc123",
        "Email Subject": "ASTRO | Low | Brute Force Authentication | Sun, May 04 2025, 04:22:55 PM | Alarm ID abc123"
    },
    {
        "Ticket Event Classification": "Multiple login failures",
        "Date & Time": "Sun, May 04 2025, 04:20:30 PM",
        "Ticket Descriptions": "Multiple login failures",
        "Ticket Assignment/Escalation": "Dear Techlab Automation Team, \n\nWe detected Multiple login failures from hostname : server2.example.com \n\nPlease Check Before take any Action, Thankyou Automation Team",
        "Severity": "medium",
        "Event / Flow count": "3 events",
        "Username": "admin456",
        "Source IP(s) & Hostname": "************* (server2.example.com)",
        "Target IP(s) & Ports": "******** (database.example.com)",
        "Recommendation": "Default recommendation text",
        "Event ID": "def456",
        "Email Subject": "ASTRO | Medium | Multiple login failures | Sun, May 04 2025, 04:20:30 PM | Alarm ID def456"
    }
]

# Test the email template with Gemini AI recommendation
def test_email_with_gemini():
    """Test the email template with Gemini AI recommendation."""
    print("Testing email template with Gemini AI recommendation...")

    # Create a mock profiler
    profiler = MockProfiler()

    # Create the HTML email body
    html_content = create_html_email_body(mock_alarms, profiler)

    # Save the HTML content to a file for inspection
    with open("email_with_gemini_test.html", "w") as f:
        f.write(html_content)

    print(f"\nHTML email content saved to email_with_gemini_test.html")
    print("Open this file in a web browser to see how the email will look.")

if __name__ == "__main__":
    test_email_with_gemini()
