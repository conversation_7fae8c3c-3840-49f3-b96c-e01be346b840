{"Google_Drive_API": {"gdrive_client_secret": "client_secrets.json", "gdrive_directory": "Astro_Reports", "gdrive_client_config_backend": "file", "gdrive_save_credentials": true, "gdrive_save_credentials_backend": "file", "gdrive_save_credentials_file": "gdrive_credentials.json", "gdrive_get_refresh_token": true, "gdrive_client_id": "${ASTRO_GDRIVE_CLIENT_ID}", "gdrive_oauth_scope": ["https://www.googleapis.com/auth/drive"], "gdrive_upload_url": "https://www.googleapis.com/upload/drive/v3/files", "gdrive_api_key": "${ASTRO_GDRIVE_API_KEY}"}, "Upload_Settings": {"auto_upload": false, "upload_frequency": "daily", "file_types": [".xlsx", ".csv", ".docx", ".pdf"], "max_file_size_mb": 10, "create_date_folders": true}}