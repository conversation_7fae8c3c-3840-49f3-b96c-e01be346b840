# Real-Time Monitoring for Astro Automation

This document explains how to set up and use real-time monitoring for the Astro Automation system.

## Overview

The Astro Automation system now supports two methods for real-time monitoring:

1. **Simple Monitoring Script** - A Python script that runs continuously in a console window
2. **Windows Service** - A background service that runs without a console window

Both methods check for alarms at regular intervals (default: 3 minutes) and process them as they are detected.

## Prerequisites

Before using real-time monitoring, make sure you have:

1. Python 3.6 or higher installed
2. All required dependencies installed (run `install_requirements.bat`)
3. Properly configured `.env` file with your credentials

## Option 1: Simple Monitoring Script (Recommended)

The simple monitoring script (`monitor.py`) is the easiest way to get started with real-time monitoring.

### How to Use

1. Run `start_monitoring.bat` to start the monitoring in a new window
2. The script will run continuously, checking for alarms at regular intervals
3. To stop monitoring, close the window or press Ctrl+C

### Command Line Options

You can also run the monitoring script directly with additional options:

```
python monitor.py [--interval SECONDS] [--debug] [--once]
```

Options:
- `--interval SECONDS` - Set the polling interval in seconds (default: 180)
- `--debug` - Enable debug mode for more detailed logging
- `--once` - Run once and exit (useful for testing)

Example:
```
python monitor.py --interval 60 --debug
```

## Option 2: Windows Service

The Windows service (`service.py`) runs in the background without a console window.

### Installation

1. Run `install_requirements.bat` to install the required dependencies
2. Open a Command Prompt as Administrator
3. Run the following command to install the service:
   ```
   python service.py install
   ```

### Managing the Service

- To start the service:
  ```
  python service.py start
  ```

- To stop the service:
  ```
  python service.py stop
  ```

- To remove the service:
  ```
  python service.py remove
  ```

- To debug the service:
  ```
  python service.py debug
  ```

## Configuration

The real-time monitoring settings can be configured in `configs/app/app_config.json`:

```json
"Service": {
    "polling_interval": 180,
    "max_consecutive_errors": 5,
    "enable_real_time": true
}
```

- `polling_interval` - Time in seconds between checks (default: 180)
- `max_consecutive_errors` - Maximum number of consecutive errors before resetting (default: 5)
- `enable_real_time` - Enable or disable real-time monitoring (default: true)

## Logs

- The simple monitoring script logs to the standard console output and the regular application logs
- The Windows service logs to `logs/service/service_YYYY-MM-DD.log`

## Troubleshooting

### The monitoring script crashes or stops unexpectedly

1. Run the script with the `--debug` flag to get more detailed logs
2. Check the application logs in the `logs` directory
3. Make sure all required credentials are properly set in the `.env` file

### The Windows service fails to start

1. Check the service logs in `logs/service/`
2. Make sure you're running the command prompt as Administrator
3. Verify that the `pywin32` package is properly installed

### API connection issues

1. Check your internet connection
2. Verify that your API credentials are correct
3. The system will automatically retry after connection failures
