# Production Deployment Guide for Astro Automation

This document provides instructions for deploying the Astro Automation application to a production environment.

## Overview

The Astro Automation application can be deployed to production in several ways:

1. **Single Execution Mode**: Run once and exit
2. **Monitoring Mode**: Continuous polling in a console window
3. **Scheduled Task**: Run at specified intervals using Windows Task Scheduler

## Prerequisites

Before deploying to production, ensure you have:

1. Python 3.6 or higher installed
2. Administrator access to the production server
3. Required API credentials and access tokens
4. Network access to required services (SMTP, API endpoints, etc.)

## Production Setup

### Step 1: Run the Production Setup Script

The `setup_production.bat` script automates the production setup process:

```
setup_production.bat
```

This script will:
1. Create a production virtual environment (`venv_prod`)
2. Install all required dependencies
3. Configure the application for production use
4. Create production run scripts
5. Set up a template `.env.production` file

### Step 2: Configure Environment Variables

Edit the `.env.production` file with your actual credentials:

```
# API Credentials
ASTRO_API_USERNAME=your_api_username
ASTRO_API_SECRET=your_api_secret

# SMTP Email Settings
ASTRO_SMTP_EMAIL=<EMAIL>
ASTRO_SMTP_PASSWORD=your_app_password
ASTRO_EMAIL_RECIPIENT=<EMAIL>

# Google Drive API
ASTRO_GDRIVE_CLIENT_ID=your_gdrive_client_id
ASTRO_GDRIVE_CLIENT_SECRET=your_gdrive_client_secret
ASTRO_GDRIVE_API_KEY=your_gdrive_api_key

# Gemini AI API
GEMINI_API_KEY=your_gemini_api_key

# Debug Settings
ASTRO_DEBUG_MODE=false
```

### Step 3: Test the Production Setup

Run the application in single execution mode to test the production setup:

```
run_production.bat
```

Verify that:
1. The application runs without errors
2. Logs are created in the `logs` directory
3. Any generated files are created in the `resources/exports` directory

## Deployment Options

### Option 1: Run as a Scheduled Task

To run the application at specified intervals:

1. Run `schedule_production_task.bat` as administrator:
   ```
   schedule_production_task.bat
   ```

2. This will create a Windows scheduled task that runs the application daily at 8:00 AM.

3. To customize the schedule, edit the `schedule_production_task.bat` file or use the Windows Task Scheduler directly.

### Option 2: Run in Monitoring Mode

To run the application in continuous monitoring mode:

1. Run `run_production_monitor.bat`:
   ```
   run_production_monitor.bat
   ```

2. This will start the application in monitoring mode, which will continuously check for alarms at regular intervals.

3. To run this as a background service, you can:
   - Use a tool like NSSM (Non-Sucking Service Manager) to create a Windows service
   - Set up a scheduled task that runs at system startup

### Option 3: Create a Windows Service

While the service.py file mentioned in the documentation appears to be missing, you can create a Windows service using:

1. **NSSM (Non-Sucking Service Manager)**:
   ```
   nssm install "Astro Automation" "C:\path\to\run_production_monitor.bat"
   ```

2. **Windows SC Command**:
   ```
   sc create "AstroAutomation" binPath= "C:\path\to\venv_prod\Scripts\python.exe C:\path\to\app.py --monitor"
   ```

## Production Maintenance

### Log Management

Logs are stored in the `logs` directory:
- Current logs are in the root of the `logs` directory
- Daily logs are in the `logs/daily` directory
- Archived logs are in the `logs/archive` directory

The application includes a log cleanup utility that can be run manually:

```
python -m src.utils.cleanup_logs --force
```

### Backup Procedures

Regularly back up the following:
1. Configuration files in the `config` directory
2. Environment variables in the `.env.production` file
3. Any custom templates in the `resources/templates` directory

### Monitoring and Alerting

1. **Log Monitoring**: Set up a log monitoring solution to alert on errors
2. **Application Monitoring**: Use the application's email notification feature to send alerts
3. **Service Monitoring**: If running as a service, monitor the service status

## Troubleshooting

### Application Fails to Start

1. Check the logs in the `logs` directory for error messages
2. Verify that all required environment variables are set correctly
3. Ensure that the virtual environment is activated before running the application

### API Connection Issues

1. Verify that the API credentials are correct
2. Check network connectivity to the API endpoints
3. Look for specific error messages in the logs

### Email Notification Issues

1. Verify SMTP settings in the configuration
2. Check that the email account has the necessary permissions
3. Test email connectivity separately

## Security Considerations

1. **Environment Variables**: Keep the `.env.production` file secure and restrict access
2. **Credentials**: Use service accounts with minimal required permissions
3. **Network Security**: Ensure the application only has access to required network resources
4. **File Permissions**: Restrict access to configuration files and logs

## Updating the Application

To update the application in production:

1. Back up the current configuration
2. Pull the latest code from version control
3. Run `setup_production.bat` to update the virtual environment
4. Test the updated application before deploying to production

## Conclusion

Following these guidelines will help ensure a smooth deployment of the Astro Automation application to production. Regular maintenance and monitoring will help identify and resolve issues quickly, ensuring the application continues to function reliably.
