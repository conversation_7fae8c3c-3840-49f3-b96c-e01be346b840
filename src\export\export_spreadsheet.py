# Standard library imports
import os
from datetime import datetime

# Third-party imports
import pandas as pd

# Project-specific imports
from src.utils.logger import logger


def export_to_excel(json_data, output_file, profiler_name):
    """
    Export data to an Excel spreadsheet.

    Args:
        json_data: List of dictionaries containing the data to export
        output_file: Base name for the output file
        profiler_name: Profiler object containing configuration
    """
    profiler = profiler_name
    logger.info(f"Creating Excel spreadsheet with {len(json_data)} items")

    # Convert data to DataFrame
    df = pd.DataFrame(json_data)

    # Reformat file name and save it
    current_datetime = datetime.now()
    formatted_datetime = current_datetime.strftime("%Y-%m-%d_%H-%M-%S")
    output_file = output_file + formatted_datetime + ".xlsx"
    file_path = os.path.join(profiler.directory, output_file)

    # Save the Excel file
    df.to_excel(file_path, index=False)
    logger.info(f"Excel spreadsheet saved to {file_path}")


def export_to_csv(json_data, output_file, profiler_name):
    """
    Export data to a CSV file.

    Args:
        json_data: List of dictionaries containing the data to export
        output_file: Base name for the output file
        profiler_name: Profiler object containing configuration
    """
    profiler = profiler_name
    logger.info(f"Creating CSV file with {len(json_data)} items")

    # Convert data to DataFrame
    df = pd.DataFrame(json_data)

    # Reformat file name and save it
    current_datetime = datetime.now()
    formatted_datetime = current_datetime.strftime("%Y-%m-%d_%H-%M-%S")
    output_file = output_file + formatted_datetime + ".csv"
    file_path = os.path.join(profiler.directory, output_file)

    # Save the CSV file
    df.to_csv(file_path, index=False)
    logger.info(f"CSV file saved to {file_path}")