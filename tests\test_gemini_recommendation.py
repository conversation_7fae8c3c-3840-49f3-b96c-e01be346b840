"""
Test script for the Gemini AI recommendation functionality.
This script tests the recommendation.py module's ability to generate recommendations using the Gemini AI API.
"""
import os
import sys

# Add the project root to the Python path to allow importing project modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment variables from .env file
from src.utils.env_loader import load_env_file
load_env_file()

# Import the recommendation module
from src.logic.recommendation import get_gemini_recommendation, recommendation_mapping_logic

# Test the Gemini AI recommendation
def test_gemini_recommendation():
    """Test the Gemini AI recommendation functionality."""
    print("Testing Gemini AI recommendation...")

    # Test with an empty logic value
    print("\nTest 1: Empty logic value")
    recommendation = get_gemini_recommendation("")
    print(f"Recommendation: {recommendation}")

    # Test with a specific logic value
    print("\nTest 2: Specific logic value")
    recommendation = get_gemini_recommendation("User reported suspicious login attempts")
    print(f"Recommendation: {recommendation}")

    # Test the recommendation mapping logic
    print("\nTest 3: Recommendation mapping logic")
    recommendation = recommendation_mapping_logic("User reported suspicious login attempts")
    print(f"Recommendation: {recommendation}")

if __name__ == "__main__":
    test_gemini_recommendation()
