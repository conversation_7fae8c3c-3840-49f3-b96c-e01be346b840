"""
Astro Automation Windows Service

This script allows the Astro Automation application to run as a Windows service.
It uses the pywin32 library to create and manage the service.

Usage:
    python service.py install   - Install the service
    python service.py start     - Start the service
    python service.py stop      - Stop the service
    python service.py remove    - Remove the service
    python service.py debug     - Run the service in debug mode (console)

Requirements:
    - pywin32 library (pip install pywin32)
    - Administrator privileges for install/remove operations
"""

import os
import sys
import time
import logging
import argparse
import traceback
import win32serviceutil
import win32service
import win32event
import servicemanager
import socket
from datetime import datetime

# Configure logging
SERVICE_LOG_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs', 'service')
if not os.path.exists(SERVICE_LOG_DIR):
    os.makedirs(SERVICE_LOG_DIR)

LOG_FILE = os.path.join(SERVICE_LOG_DIR, f'service_{datetime.now().strftime("%Y-%m-%d")}.log')
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('AstroAutomationService')


class AstroAutomationService(win32serviceutil.ServiceFramework):
    """Windows Service for Astro Automation."""
    
    _svc_name_ = "AstroAutomation"
    _svc_display_name_ = "Astro Automation Monitoring Service"
    _svc_description_ = "Continuous monitoring service for Astro Automation"
    
    def __init__(self, args):
        """Initialize the service."""
        win32serviceutil.ServiceFramework.__init__(self, args)
        self.hWaitStop = win32event.CreateEvent(None, 0, 0, None)
        socket.setdefaulttimeout(60)
        self.is_running = False
        self.polling_interval = 180  # Default polling interval in seconds
        
        # Load configuration
        try:
            from src.utils.config_loader import get_app_config
            app_config = get_app_config()
            self.polling_interval = app_config.get('Service', {}).get('polling_interval', 180)
            logger.info(f"Loaded polling interval from config: {self.polling_interval} seconds")
        except Exception as e:
            logger.error(f"Error loading configuration: {str(e)}")
            logger.error(traceback.format_exc())
    
    def SvcStop(self):
        """Stop the service."""
        self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
        win32event.SetEvent(self.hWaitStop)
        self.is_running = False
        logger.info("Service stop requested")
    
    def SvcDoRun(self):
        """Run the service."""
        servicemanager.LogMsg(
            servicemanager.EVENTLOG_INFORMATION_TYPE,
            servicemanager.PYS_SERVICE_STARTED,
            (self._svc_name_, '')
        )
        self.is_running = True
        self.main()
    
    def main(self):
        """Main service function."""
        logger.info("Service started")
        
        # Load environment variables
        try:
            from src.utils.env_loader import load_env_file
            load_env_file()
            logger.info("Environment variables loaded")
        except Exception as e:
            logger.error(f"Error loading environment variables: {str(e)}")
            logger.error(traceback.format_exc())
        
        # Import the main application modules
        try:
            from src.utils.logger import logger as app_logger, info, debug
            from src.logic import profiler_controller
            import src.main as astro_main
            
            # Set up logging
            app_logger.set_level(app_logger.INFO)
            logger.info("Application modules loaded")
        except Exception as e:
            logger.error(f"Error importing application modules: {str(e)}")
            logger.error(traceback.format_exc())
            return
        
        # Main service loop
        consecutive_errors = 0
        max_consecutive_errors = 5
        
        while self.is_running:
            try:
                # Log start of monitoring cycle
                current_time = datetime.now()
                logger.info(f"Starting monitoring cycle at {current_time}")
                
                # Manage log files
                try:
                    astro_main.manage_log_files()
                except Exception as e:
                    logger.error(f"Error managing log files: {str(e)}")
                    logger.error(traceback.format_exc())
                
                # Get the list of profilers to process
                profiler_list = profiler_controller.profiler_list
                
                # Process each profiler
                for profile in profiler_list:
                    logger.info(f"Processing profile: {profile}")
                    try:
                        # Process the profiler
                        result = astro_main.process_profiler(profile)
                        if result:
                            logger.info(f"Successfully processed profile {profile}")
                        else:
                            logger.info(f"No alarms detected for profile {profile}")
                    except Exception as e:
                        logger.error(f"Error processing profile {profile}: {str(e)}")
                        logger.error(traceback.format_exc())
                
                # Reset consecutive errors counter on successful run
                consecutive_errors = 0
                
                # Log end of monitoring cycle
                end_time = datetime.now()
                duration = (end_time - current_time).total_seconds()
                logger.info(f"Monitoring cycle completed in {duration:.2f} seconds")
                
                # Wait for the next polling interval or service stop
                wait_start = time.time()
                while self.is_running and (time.time() - wait_start) < self.polling_interval:
                    # Check for stop signal every second
                    rc = win32event.WaitForSingleObject(self.hWaitStop, 1000)
                    if rc == win32event.WAIT_OBJECT_0:
                        # Stop signal received
                        logger.info("Stop signal received during wait")
                        break
            
            except Exception as e:
                consecutive_errors += 1
                logger.error(f"Error in monitoring cycle: {str(e)}")
                logger.error(traceback.format_exc())
                
                logger.warning(f"Consecutive errors: {consecutive_errors}/{max_consecutive_errors}")
                
                if consecutive_errors >= max_consecutive_errors:
                    logger.warning(f"Maximum consecutive errors ({max_consecutive_errors}) reached. Resetting...")
                    # Reset error counter
                    consecutive_errors = 0
                    # Wait a bit before continuing
                    time.sleep(60)
                
                # Wait before retrying after an error
                time.sleep(30)
        
        logger.info("Service stopped")


def parse_command_line():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Astro Automation Windows Service')
    parser.add_argument('command', choices=['install', 'start', 'stop', 'remove', 'debug'],
                      help='Command to execute')
    return parser.parse_args()


if __name__ == '__main__':
    if len(sys.argv) == 1:
        servicemanager.Initialize()
        servicemanager.PrepareToHostSingle(AstroAutomationService)
        servicemanager.StartServiceCtrlDispatcher()
    else:
        args = parse_command_line()
        
        if args.command == 'debug':
            service = AstroAutomationService([])
            service.is_running = True
            service.main()
        else:
            win32serviceutil.HandleCommandLine(AstroAutomationService)
