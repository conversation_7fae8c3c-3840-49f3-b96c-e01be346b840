# Standard library imports
import importlib
import os
from typing import List, Dict, Any

# Project-specific imports
from src.profilers import profiler_3
from src.logic import api_method
from src.utils.logger import logger

# Configuration
DEFAULT_PROFILER = "profiler_3"
SUPPORTED_PROFILERS = ["profiler_3"]

# This list is used by main.py to iterate through available profilers
profiler_list = SUPPORTED_PROFILERS

# Check if we're in debug mode - default to True for troubleshooting
DEBUG_MODE = os.environ.get("ASTRO_DEBUG_MODE", "true").lower() in ("true", "1", "yes", "y")


def data(profiler_file_name: str) -> List[Dict[str, Any]]:
    """
    Get data from the specified profiler.

    Args:
        profiler_file_name: Name of the profiler to use

    Returns:
        List of filtered events from the API

    Note:
        Currently only supports profiler_3. If an unsupported profiler is
        specified, it will log a warning and use the default profiler.
    """
    if profiler_file_name not in SUPPORTED_PROFILERS:
        log_message = f"Warning: Profiler '{profiler_file_name}' not supported. Using {DEFAULT_PROFILER} instead."
        if DEBUG_MODE:
            print(f"DEBUG: {log_message}")
        logger.warning(log_message)
        profiler_file_name = DEFAULT_PROFILER

    # Import the profiler module dynamically
    try:
        profiler_module = importlib.import_module(f"src.profilers.{profiler_file_name}")

        # Check if the profiler has severity_levels configuration
        severity_levels = getattr(profiler_module, "severity_levels", None)

        # If severity_levels is not defined in the profiler, check if it's in the config
        if severity_levels is None and hasattr(profiler_module, "config") and isinstance(profiler_module.config, dict):
            severity_levels = profiler_module.config.get("Triggers", {}).get("severity_levels", ["medium", "high"])

        if DEBUG_MODE:
            print(f"DEBUG: Using severity levels from profiler: {severity_levels}")
            print(f"DEBUG: Bearer URL: {profiler_module.bearer_req_url}")
            print(f"DEBUG: API URL: {profiler_module.api_request_url}")

        # Get data from API
        try:
            data = api_method.api_filter(
                profiler_module.bearer_req_url,
                profiler_module.api_request_url,
                severity_levels=severity_levels
            )

            if DEBUG_MODE:
                print(f"DEBUG: Retrieved {len(data)} alarms from API")
                if data and len(data) > 0:
                    print(f"DEBUG: First alarm sample: {list(data[0].keys())}")

            return data
        except Exception as e:
            error_message = f"Error retrieving data from API: {e}"
            if DEBUG_MODE:
                print(f"DEBUG: {error_message}")
                print(f"DEBUG: Generating mock data due to API error")
            logger.error(error_message)

            # Generate mock data if API call fails
            from src.logic.api_base import ApiBase
            mock_api = ApiBase(debug_mode=True)
            mock_api.use_mock_data = True
            mock_data = mock_api._generate_mock_data(severity_levels or ["low", "medium", "high"])

            if DEBUG_MODE:
                print(f"DEBUG: Generated {len(mock_data)} mock alarms")

            return mock_data

    except (ImportError, AttributeError) as e:
        error_message = f"Error loading profiler {profiler_file_name}: {e}"
        if DEBUG_MODE:
            print(f"DEBUG: {error_message}")
        logger.error(error_message)

        # Fallback to profiler_3 as a last resort
        if DEBUG_MODE:
            print(f"DEBUG: Falling back to profiler_3")

        try:
            # Try to get data from profiler_3 using the instance
            profiler = profiler_3.get_instance()
            return api_method.api_filter(profiler.bearer_req_url, profiler.api_request_url)
        except Exception as e:
            error_message = f"Error retrieving data from fallback profiler: {e}"
            if DEBUG_MODE:
                print(f"DEBUG: {error_message}")
                print(f"DEBUG: Generating mock data as last resort")
            logger.error(error_message)

            # Generate mock data as a last resort
            from src.logic.api_base import ApiBase
            mock_api = ApiBase(debug_mode=True)
            mock_api.use_mock_data = True
            mock_data = mock_api._generate_mock_data(["low", "medium", "high"])

            if DEBUG_MODE:
                print(f"DEBUG: Generated {len(mock_data)} mock alarms as last resort")

            return mock_data

