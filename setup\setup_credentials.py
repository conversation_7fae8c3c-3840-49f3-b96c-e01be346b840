# Standard library imports
import os
import json
import getpass
import argparse
from pathlib import Path

def main():
    """
    Set up environment variables for secure credential storage.

    This script helps users set up environment variables for storing credentials securely.
    It reads the configuration files and prompts the user for credentials that should be
    stored as environment variables.
    """
    parser = argparse.ArgumentParser(description="Set up environment variables for secure credential storage")
    parser.add_argument("--config", default="configs_file/config_umwt.json", help="Path to the configuration file")
    parser.add_argument("--output", default=".env", help="Path to the output .env file")
    args = parser.parse_args()

    # Load the configuration file
    try:
        with open(args.config, 'r') as f:
            config_data = json.load(f)
    except Exception as e:
        print(f"Error loading configuration file: {e}")
        return

    # Create a dictionary to store environment variables
    env_vars = {}

    # Add API credentials
    if "API_Credentials" in config_data:
        api_username = config_data["API_Credentials"].get("api_username", "")
        api_secret = config_data["API_Credentials"].get("api_secret", "")

        print("\n=== API Credentials ===")
        new_api_username = input(f"API Username [{api_username}]: ") or api_username
        new_api_secret = getpass.getpass(f"API Secret [leave empty to keep current]: ") or api_secret

        env_vars["ASTRO_API_USERNAME"] = new_api_username
        env_vars["ASTRO_API_SECRET"] = new_api_secret

    # Add SMTP credentials
    if "SMTP" in config_data:
        smtp_email = config_data["SMTP"].get("email", "")
        smtp_password = config_data["SMTP"].get("app_pass", "")

        print("\n=== SMTP Credentials ===")
        new_smtp_email = input(f"SMTP Email [{smtp_email}]: ") or smtp_email
        new_smtp_password = getpass.getpass(f"SMTP Password [leave empty to keep current]: ") or smtp_password

        env_vars["ASTRO_SMTP_EMAIL"] = new_smtp_email
        env_vars["ASTRO_SMTP_PASSWORD"] = new_smtp_password

        # Also use SMTP email as the default for Email sender
        if "Email" in config_data:
            email_sender = config_data["Email"].get("sender", "")
            if not email_sender and new_smtp_email:
                env_vars["ASTRO_EMAIL_SENDER"] = new_smtp_email
                print(f"Using SMTP email as the default email sender: {new_smtp_email}")

    # Add Email sender if different from SMTP email
    if "Email" in config_data:
        email_sender = config_data["Email"].get("sender", "")
        smtp_email = env_vars.get("ASTRO_SMTP_EMAIL", "")

        # Only prompt if email_sender is not empty and different from SMTP email
        if email_sender and email_sender != smtp_email:
            print("\n=== Email Sender ===")
            new_email_sender = input(f"Email Sender [{email_sender}]: ") or email_sender

            if new_email_sender != smtp_email:
                env_vars["ASTRO_EMAIL_SENDER"] = new_email_sender

    # Add Google Drive API credentials
    if "Google_Drive_API" in config_data:
        gdrive_client_id = config_data["Google_Drive_API"].get("gdrive_client_id", "")
        gdrive_client_secret = config_data["Google_Drive_API"].get("gdrive_client_secret", "")
        gdrive_api_key = config_data["Google_Drive_API"].get("gdrive_api_key", "")

        print("\n=== Google Drive API Credentials ===")
        new_gdrive_client_id = input(f"Google Drive Client ID [{gdrive_client_id}]: ") or gdrive_client_id
        new_gdrive_client_secret = getpass.getpass(f"Google Drive Client Secret [leave empty to keep current]: ") or gdrive_client_secret
        new_gdrive_api_key = input(f"Google Drive API Key [{gdrive_api_key}]: ") or gdrive_api_key

        env_vars["ASTRO_GDRIVE_CLIENT_ID"] = new_gdrive_client_id
        env_vars["ASTRO_GDRIVE_CLIENT_SECRET"] = new_gdrive_client_secret
        env_vars["ASTRO_GDRIVE_API_KEY"] = new_gdrive_api_key

    # Add encryption key
    print("\n=== Encryption Key ===")
    encryption_key = getpass.getpass("Encryption Key (for secure credential storage): ")
    if encryption_key:
        env_vars["ASTRO_ENCRYPTION_KEY"] = encryption_key

    # Write environment variables to .env file
    with open(args.output, 'w') as f:
        for key, value in env_vars.items():
            f.write(f"{key}={value}\n")

    print(f"\nEnvironment variables written to {args.output}")
    print("To use these environment variables:")
    print("1. For development: Use a tool like python-dotenv to load them")
    print("2. For production: Set these environment variables in your system")
    print("\nIMPORTANT: Keep your .env file secure and do not commit it to version control!")


if __name__ == "__main__":
    main()
