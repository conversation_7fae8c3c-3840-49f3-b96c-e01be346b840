"""
Log Cleanup Utility for Astro Automation

This script provides a standalone utility to clean up log files to prevent
them from taking up too much space on the server.

Usage:
    python -m src.utils.cleanup_logs [--days-to-keep DAYS] [--max-archive-days DAYS] [--force]

Options:
    --days-to-keep DAYS       Number of days to keep logs before archiving (default: 7)
    --max-archive-days DAYS   Maximum number of days to keep archived logs (default: 30)
    --force                   Force cleanup even if the application is running
"""

import os
import sys
import argparse
import logging
import psutil
from datetime import datetime

# Import the log manager
from src.utils.env_loader import load_env_file
from src.utils.config_loader import get_logging_config
from src.utils.log_manager import manage_logs

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("log_cleanup")


def is_application_running():
    """Check if the main application is running."""
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            # Check for monitor.py or any Python process running our application
            cmdline = proc.info.get('cmdline', [])
            if cmdline and len(cmdline) > 1:
                if 'python' in cmdline[0].lower() and any(x in str(cmdline) for x in ['monitor.py', 'main.py']):
                    return True
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    return False


def cleanup_logs(days_to_keep, max_archive_days, force=False):
    """
    Clean up log files.

    Args:
        days_to_keep: Number of days to keep logs before archiving
        max_archive_days: Maximum number of days to keep archived logs
        force: Force cleanup even if the application is running

    Returns:
        Dictionary with results of the operation
    """
    # Check if the application is running
    if is_application_running() and not force:
        logger.warning("Application is running. Use --force to clean up logs anyway.")
        return {
            "archived_directories": [],
            "deleted_directories": 0,
            "status": "skipped"
        }

    # Load environment variables
    load_env_file()

    # Get logging configuration
    logging_config = get_logging_config()

    # Get log directory from config or use default
    logs_dir = logging_config.get("Logging", {}).get("base_log_dir", "logs")

    # Run log management
    result = manage_logs(
        logs_dir=logs_dir,
        daily_dir="daily",
        archive_dir="archive",
        days_to_keep=days_to_keep,
        max_archive_days=max_archive_days
    )

    result["status"] = "completed"
    return result


def main():
    """Main function for the log cleanup script."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Clean up log files for Astro Automation")
    parser.add_argument(
        "--days-to-keep",
        type=int,
        default=7,
        help="Number of days to keep logs before archiving (default: 7)"
    )
    parser.add_argument(
        "--max-archive-days",
        type=int,
        default=30,
        help="Maximum number of days to keep archived logs (default: 30)"
    )
    parser.add_argument(
        "--force",
        action="store_true",
        help="Force cleanup even if the application is running"
    )
    args = parser.parse_args()

    # Print start message
    print(f"Starting log cleanup at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"  - Days to keep logs: {args.days_to_keep}")
    print(f"  - Max archive days: {args.max_archive_days}")
    print(f"  - Force cleanup: {'Yes' if args.force else 'No'}")
    print()

    # Clean up logs
    result = cleanup_logs(args.days_to_keep, args.max_archive_days, args.force)

    # Print results
    if result["status"] == "skipped":
        print("Log cleanup skipped because the application is running.")
        print("Use --force to clean up logs anyway.")
        return 1

    print("Log cleanup completed successfully:")
    print(f"  - Archived {len(result['archived_directories'])} directories")
    if result['archived_directories']:
        print(f"    - {', '.join(result['archived_directories'])}")
    print(f"  - Deleted {result['deleted_directories']} directories")

    return 0


if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\nLog cleanup interrupted by user.")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Error during log cleanup: {str(e)}", exc_info=True)
        print(f"\nError during log cleanup: {str(e)}")
        sys.exit(1)
