"""
Test script for the batch Gemini AI recommendation functionality.
This script tests the ability to generate a single recommendation for multiple alarms.
"""
import os
import sys
import json

# Add the project root to the Python path to allow importing project modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment variables from .env file
from src.utils.env_loader import load_env_file
load_env_file()

# Import the recommendation module
from src.logic.recommendation import get_batch_gemini_recommendation

# Create some mock alarm data for testing
mock_alarms = [
    {
        "Ticket Event Classification": "Brute Force Authentication",
        "Severity": "low",
        "Username": "user123",
        "Source IP(s) & Hostname": "************* (workstation1.example.com)",
        "Event ID": "abc123"
    },
    {
        "Ticket Event Classification": "Multiple login failures",
        "Severity": "medium",
        "Username": "admin456",
        "Source IP(s) & Hostname": "************* (server2.example.com)",
        "Event ID": "def456"
    },
    {
        "Ticket Event Classification": "Suspicious login activity",
        "Severity": "high",
        "Username": "system789",
        "Source IP(s) & Hostname": "192.168.1.300 (unknown.example.com)",
        "Event ID": "ghi789"
    }
]

# Test the batch Gemini AI recommendation
def test_batch_recommendation():
    """Test the batch Gemini AI recommendation functionality."""
    print("Testing batch Gemini AI recommendation...")

    # Get a recommendation for all alarms
    recommendation = get_batch_gemini_recommendation(mock_alarms)

    # Print the recommendation
    print("\nBatch Recommendation:")
    print(recommendation)

    # Save the recommendation to a file for inspection
    with open("batch_recommendation_test.txt", "w") as f:
        f.write(recommendation if recommendation else "Failed to get recommendation")

    print(f"\nRecommendation saved to batch_recommendation_test.txt")

if __name__ == "__main__":
    test_batch_recommendation()
