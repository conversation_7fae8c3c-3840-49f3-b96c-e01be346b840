# Standard library imports
import os
import json
from typing import Dict, List, Optional, Any

# Third-party imports
import requests

# Project-specific imports
from src.utils.logger import logger

# Constants
GEMINI_API_ENDPOINT = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent"
DEFAULT_HEADERS = {"Content-Type": "application/json"}

# HTML formatting guidelines for Gemini prompts
HTML_FORMATTING_GUIDELINES = """
IMPORTANT FORMATTING INSTRUCTIONS:
1. Format the response as clean HTML that can be directly included in an email.
2. DO NOT include any markdown code block markers (```html or ```).
3. DO NOT use markdown-style formatting with asterisks (* or **).
4. Use proper HTML formatting with these guidelines:
   - Use <h2> tags for main section headings (e.g., <h2>Security Alert</h2>)
   - Use <h3> tags for subsection headings
   - Use <p> tags for paragraphs
   - Use <ol> and <li> for numbered lists
   - Use <ul> and <li> for bullet points
   - Use <div style="margin-left: 20px;"> for indentation
   - Use <span style="color: #FF5733;"> for high severity items
   - Use <span style="color: #FFC300;"> for medium severity items
   - Use <span style="color: #2ECC71;"> for low severity items
5. Keep the HTML compact with minimal extra spacing or line breaks.
"""


def _call_gemini_api(prompt: str) -> Optional[str]:
    """
    Helper function to call the Gemini AI API.

    Args:
        prompt: The prompt to send to the Gemini AI API

    Returns:
        str: The response text from Gemini AI or None if the API call fails
    """
    # Get the API key from environment variables
    api_key = os.environ.get("GEMINI_API_KEY")

    if not api_key:
        logger.warning("GEMINI_API_KEY not found in environment variables")
        return None

    # Remove quotes if present in the API key
    api_key = api_key.strip('"')

    # Gemini API endpoint with API key
    url = f"{GEMINI_API_ENDPOINT}?key={api_key}"

    # Prepare the request payload
    payload = {
        "contents": [{
            "parts": [{"text": prompt}]
        }]
    }

    # Make the API request
    try:
        logger.debug("Sending request to Gemini AI")
        response = requests.post(url, headers=DEFAULT_HEADERS, data=json.dumps(payload))
        response.raise_for_status()  # Raise an exception for HTTP errors

        # Parse the response
        response_data = response.json()

        # Extract the generated text from the response
        if "candidates" in response_data and response_data["candidates"]:
            candidate = response_data["candidates"][0]
            if "content" in candidate and "parts" in candidate["content"]:
                parts = candidate["content"]["parts"]
                if parts and "text" in parts[0]:
                    # Clean up the response by removing markdown code block markers
                    response_text = parts[0]["text"]
                    response_text = response_text.replace("```html", "").replace("```", "").strip()
                    logger.info("Successfully received response from Gemini AI")
                    return response_text

        logger.warning(f"Unexpected response format from Gemini API: {response_data}")
        return None

    except Exception as e:
        logger.error(f"Error calling Gemini API: {str(e)}")
        return None


def get_gemini_recommendation(logic_value: Optional[str] = None) -> Optional[str]:
    """
    Get a recommendation from the Gemini AI API for a single alarm.

    Args:
        logic_value: The logic value to use for generating the recommendation

    Returns:
        str: The recommendation text from Gemini AI or None if the API call fails
    """
    # Prepare the prompt based on the logic value
    prompt = """Generate a security recommendation for a user who experienced a login failure.

"""
    # Add the HTML formatting guidelines
    prompt += HTML_FORMATTING_GUIDELINES

    # Add specific structure for this recommendation
    prompt += """
5. Structure the email with these sections:
   - A left-aligned title using <h1>Security Alert: Login Failure</h1>
   - Brief description of the issue (keep it concise)
   - Immediate actions (numbered steps)
   - Long-term recommendations (bullet points)
   - A brief conclusion
"""

    # Add additional context if provided
    if logic_value:
        prompt += f"\nAdditional context: {logic_value}"

    # Call the Gemini API
    return _call_gemini_api(prompt)


def get_batch_gemini_recommendation(alarms: List[Dict[str, Any]]) -> Optional[str]:
    """
    Get a single recommendation from Gemini AI for multiple alarms.

    Args:
        alarms: List of alarm dictionaries

    Returns:
        str: The recommendation text from Gemini AI or None if the API call fails
    """
    # Extract relevant information from alarms
    alarm_summary = []
    for alarm in alarms:
        if isinstance(alarm, dict):
            event_type = alarm.get("Ticket Event Classification", "Unknown event")
            severity = alarm.get("Severity", "Unknown severity")
            username = alarm.get("Username", "Unknown user")
            source = alarm.get("Source IP(s) & Hostname", "Unknown source")

            alarm_summary.append(f"- {event_type} (Severity: {severity}) for user {username} from {source}")

    # Create a summary of all alarms
    alarm_summary_text = "\n".join(alarm_summary)

    # Prepare the prompt with alarm summary
    prompt = f"""Generate a comprehensive security recommendation for the following security alarms:

{alarm_summary_text}

Please provide actionable steps that should be taken to address these security concerns.
Include both immediate actions and long-term security improvements.

"""
    # Add the HTML formatting guidelines
    prompt += HTML_FORMATTING_GUIDELINES

    # Add specific structure for batch recommendations
    prompt += """
5. Structure the email with these sections:
   - A left-aligned title using <h1>Security Incident Response</h1>
   - Brief introduction summarizing the security situation (keep it concise)
   - Summary of detected alarms in a clean, numbered list format
   - Detailed analysis for each alarm type with:
     > Clear description of the issue
     > Immediate actions (numbered steps)
     > Long-term recommendations (bullet points)
   - Conclusion with priority recommendations
6. For the alarm summary, use a consistent format for each item that clearly shows:
   - The alarm type
   - The severity level (with appropriate color coding)
   - The affected user/resource
   - The source of the alarm
"""

    # Call the Gemini API
    logger.info(f"Sending batch recommendation request for {len(alarms)} alarms to Gemini AI")
    return _call_gemini_api(prompt)


def recommendation_mapping_logic(logic_value: Optional[str] = None) -> str:
    """
    Map logic values to recommendations.

    Args:
        logic_value: The logic value to map

    Returns:
        str: The recommendation text
    """
    # Try to get a recommendation from Gemini AI
    gemini_recommendation = get_gemini_recommendation(logic_value)

    # If Gemini AI recommendation is available, use it
    if gemini_recommendation:
        logger.info("Using Gemini AI recommendation")
        return gemini_recommendation

    # Otherwise, fall back to the default recommendation
    logger.info("Using default recommendation (Gemini AI recommendation not available)")
    return recommend_user_verification


# Default recommendations as fallbacks
recommend_something_else = """

"""
recommend_user_verification = """
<h1>SECURITY ALERT: Account Login Verification Required</h1>

<p>Dear Security Team,</p>

<p>We have detected login activity that requires verification. Please review the information below and take appropriate action.</p>

<h2>VERIFICATION REQUIRED:</h2>
<p>Please confirm if the user has attempted to log into their account and verify this activity.</p>

<h2>RECOMMENDED ACTIONS:</h2>

<ol>
  <li><strong>Login Failure Resolution:</strong>
    <div style="margin-left: 20px;">
      <ul>
        <li>If confirmed as legitimate user activity: No further action required. Please close the ticket.</li>
        <li>If activity is suspicious: Proceed to account security measures below.</li>
      </ul>
    </div>
  </li>

  <li><strong>Account Login Failure with Account Lockout:</strong>
    <div style="margin-left: 20px;">
      <ul>
        <li>Request ID Team to enable the user account if it has been disabled.</li>
        <li>Have the user reset their password immediately.</li>
        <li>Implement Multi-Factor Authentication (MFA) enforcement for this account.</li>
      </ul>
    </div>
  </li>

  <li><strong>If User Confirms Unauthorized Login Activity:</strong>
    <div style="margin-left: 20px;">
      <ul>
        <li>Immediately log out from all related accounts.</li>
        <li>Reset password using a secure, unique combination.</li>
        <li>Enable MFA for additional protection.</li>
        <li>Review account for any unauthorized changes.</li>
      </ul>
    </div>
  </li>
</ol>

<h2>SECURITY RECOMMENDATIONS:</h2>
<ul>
  <li>Avoid using corporate email addresses to sign up on non-business websites.</li>
  <li>Implement a password manager to generate and store complex passwords securely.</li>
  <li>Consider implementing organization-wide security awareness training.</li>
  <li>Review access logs regularly to identify unusual patterns.</li>
</ul>

<p>Please address this matter promptly to maintain the security of your systems.</p>

<p><strong>Security Operations Team</strong></p>
"""