@echo off
echo Starting Astro Automation real-time monitoring...
echo.

REM Check if Python is available
where python >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Python not found. Please make sure Python is installed and in your PATH.
    pause
    exit /b 1
)

REM Run log cleanup with --force flag to ensure it runs even if another instance is running
echo Running log cleanup before starting monitoring...
python -m src.utils.cleanup_logs --force
echo Log cleanup completed.
echo.

REM Start the monitoring script in a new window
start "Astro Automation Monitor" cmd /c "python app.py --monitor && pause"

echo.
echo Monitoring started in a new window.
echo You can close this window.
echo.
pause
