"""
Test script for the recommendation display in email templates.
This script tests how recommendations are displayed based on the number of alarms.

For a single alarm:
- The recommendation appears as a row in the alarm table
- The word "Recommendation" is in the left column (teal background)
- The recommendation content is in the right column

For multiple alarms:
- Each alarm is displayed without its recommendation
- A single recommendation section is shown at the end of all alarms
"""
import os
import sys
import json
from datetime import datetime

# Add the project root to the Python path to allow importing project modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment variables from .env file
from src.utils.env_loader import load_env_file
load_env_file()

# Import the necessary modules
from src.export.email_server import create_html_email_body
from src.logic.recommendation import get_batch_gemini_recommendation, get_gemini_recommendation

# Create a mock profiler class for testing
class MockProfiler:
    def __init__(self):
        self.document_title = "Security Alert Report"
        self.config = {
            "Email": {
                "use_gemini_ai": True,
                "use_template_in_body": True
            }
        }

# Create mock alarm data for testing single alarm
mock_single_alarm = [
    {
        "Ticket Event Classification": "Anomalous Access Failure",
        "Date & Time": "Sun, May 04 2025, 04:22:55 PM",
        "Ticket Descriptions": "Anomalous Access Failure",
        "Ticket Assignment/Escalation": "Dear Techlab SOC Team, \n\nWe detected Anomalous Access Failure from hostname : WORKSTATION1 \n\nPlease Check Before take any Action, Thankyou SOC Team",
        "Severity": "Low",
        "Event / Flow count": "5 events",
        "Username": "user123",
        "Source IP(s) & Hostname": "************* (WORKSTATION1)",
        "Target IP(s) & Ports": "******** (AUTH-SERVER)",
        "Recommendation": "<h2>Detailed Analysis and Recommendations</h2><p>This alarm indicates a user or resource is failing to access resources, potentially due to misconfiguration, permission issues, or malicious activity.</p><h3>Immediate Actions:</h3><ol><li>Investigate the IAM roles and permissions associated with the failing users.</li><li>Check CloudTrail logs for detailed error messages and context.</li><li>Verify the user has the necessary permissions to access the required resources.</li></ol>",
        "Event ID": "abc123",
        "Email Subject": "ASTRO | Low | Anomalous Access Failure | Sun, May 04 2025, 04:22:55 PM | Alarm ID abc123"
    }
]

# Create mock alarm data for testing multiple alarms
mock_multiple_alarms = [
    {
        "Ticket Event Classification": "Anomalous Access Failure",
        "Date & Time": "Sun, May 04 2025, 04:22:55 PM",
        "Ticket Descriptions": "Anomalous Access Failure",
        "Ticket Assignment/Escalation": "Dear Techlab SOC Team, \n\nWe detected Anomalous Access Failure from hostname : WORKSTATION1 \n\nPlease Check Before take any Action, Thankyou SOC Team",
        "Severity": "Low",
        "Event / Flow count": "5 events",
        "Username": "user123",
        "Source IP(s) & Hostname": "************* (WORKSTATION1)",
        "Target IP(s) & Ports": "******** (AUTH-SERVER)",
        "Recommendation": "<h2>Detailed Analysis and Recommendations</h2><p>This alarm indicates a user or resource is failing to access resources, potentially due to misconfiguration, permission issues, or malicious activity.</p><h3>Immediate Actions:</h3><ol><li>Investigate the IAM roles and permissions associated with the failing users.</li><li>Check CloudTrail logs for detailed error messages and context.</li><li>Verify the user has the necessary permissions to access the required resources.</li></ol>",
        "Event ID": "abc123",
        "Email Subject": "ASTRO | Low | Anomalous Access Failure | Sun, May 04 2025, 04:22:55 PM | Alarm ID abc123"
    },
    {
        "Ticket Event Classification": "Brute Force Permission Enumeration",
        "Date & Time": "Sun, May 04 2025, 04:20:30 PM",
        "Ticket Descriptions": "Brute Force Permission Enumeration",
        "Ticket Assignment/Escalation": "Dear Techlab SOC Team, \n\nWe detected Brute Force Permission Enumeration from hostname : SERVER2 \n\nPlease Check Before take any Action, Thankyou SOC Team",
        "Severity": "Medium",
        "Event / Flow count": "3 events",
        "Username": "admin456",
        "Source IP(s) & Hostname": "************* (SERVER2)",
        "Target IP(s) & Ports": "******** (DATABASE-SERVER)",
        "Recommendation": "<h2>Detailed Analysis and Recommendations</h2><p>This alarm indicates a user or resource is failing to access resources, potentially due to misconfiguration, permission issues, or malicious activity.</p><h3>Immediate Actions:</h3><ol><li>Investigate the IAM roles and permissions associated with the failing users.</li><li>Check CloudTrail logs for detailed error messages and context.</li><li>Verify the user has the necessary permissions to access the required resources.</li></ol>",
        "Event ID": "def456",
        "Email Subject": "ASTRO | Medium | Brute Force Permission Enumeration | Sun, May 04 2025, 04:20:30 PM | Alarm ID def456"
    }
]

# Test the email template with a single alarm
def test_single_alarm_recommendation():
    """Test the email template with a single alarm."""
    print("Testing email template with a single alarm...")

    # Create a mock profiler
    profiler = MockProfiler()

    # Create the HTML email body
    html_content = create_html_email_body(mock_single_alarm, profiler)

    # Save the HTML content to a file for inspection
    with open("single_alarm_recommendation.html", "w") as f:
        f.write(html_content)

    print(f"\nHTML email content saved to single_alarm_recommendation.html")
    print("Open this file in a web browser to see how the email will look.")

# Test the email template with multiple alarms
def test_multiple_alarms_recommendation():
    """Test the email template with multiple alarms."""
    print("Testing email template with multiple alarms...")

    # Create a mock profiler
    profiler = MockProfiler()

    # Create the HTML email body
    html_content = create_html_email_body(mock_multiple_alarms, profiler)

    # Save the HTML content to a file for inspection
    with open("multiple_alarms_recommendation.html", "w") as f:
        f.write(html_content)

    print(f"\nHTML email content saved to multiple_alarms_recommendation.html")
    print("Open this file in a web browser to see how the email will look.")

if __name__ == "__main__":
    test_single_alarm_recommendation()
    test_multiple_alarms_recommendation()
