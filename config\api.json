{"API_URL": {"bearer_request_url": "https://api.example.com/auth", "api_request_url": "https://api.example.com/alerts"}, "API_Credentials": {"api_username": "${ASTRO_API_USERNAME}", "api_secret": "${ASTRO_API_SECRET}"}, "API_Settings": {"timeout": 30, "max_retries": 3, "retry_delay": 5, "use_mock_data": false}, "Filters": {"default_status": "open", "default_severity_levels": ["medium", "high"], "max_results": 100}, "Gemini": {"api_key": "${GEMINI_API_KEY}", "model": "gemini-2.0-flash", "api_url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent", "max_tokens": 1024, "temperature": 0.7}}