@echo off
echo Astro Automation Log Cleanup Utility
echo ===================================
echo.

REM Check if Python is available
where python >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Python not found. Please make sure Python is installed and in your PATH.
    pause
    exit /b 1
)

REM Run the log cleanup script
python -m src.utils.cleanup_logs %*

echo.
if %ERRORLEVEL% equ 0 (
    echo Log cleanup completed successfully.
) else (
    echo Log cleanup failed or was skipped.
)

echo.
pause
