"""
API module that provides functionality for interacting with the API.
This module uses the ApiBase class for common functionality.
"""
# Standard library imports
import os

# Project-specific imports
from src.utils.logger import logger
from src.utils.config_loader import ConfigLoader
from src.logic.api_base import ApiBase, AuthenticationError, DataFetchError

# Constants
CONFIG_PATH = "config/profiles/config_umwt.json"
DEFAULT_SEVERITY_LEVELS = ["medium", "high"]
DEFAULT_STATUS_FILTER = "open"

# Load config from JSON file
try:
    config = ConfigLoader.load(CONFIG_PATH)

    # API configuration from config
    bearer_req_url = config.get('API_URL', {}).get('bearer_request_url', '')
    api_client_id = config.get('API_Credentials', {}).get('api_username', '')
    api_client_secret = config.get('API_Credentials', {}).get('api_secret', '')

    # Get debug mode from environment or config
    debug_mode = os.environ.get("ASTRO_DEBUG_MODE", "").lower() in ("true", "1", "yes", "y")
    use_mock_data = os.environ.get("ASTRO_USE_MOCK_DATA", "").lower() in ("true", "1", "yes", "y")

    logger.info(f"Loaded API configuration from {CONFIG_PATH}")
except Exception as e:
    logger.error(f"Error loading API configuration: {e}")
    # Default values if config loading fails
    bearer_req_url = "https://astro.alienvault.cloud/api/2.0/oauth/token"
    api_client_id = ""
    api_client_secret = ""
    debug_mode = True
    use_mock_data = True
    logger.warning("Using default API configuration values")

# Create API instance
api = ApiBase(debug_mode=debug_mode)
api.use_mock_data = use_mock_data


def trigger_email_condition(condition, bearer_url=None):
    """
    Check if any alarms meet the condition specified in the configuration.
    Uses a safe condition parser instead of eval() for security.

    Args:
        condition: String condition to evaluate for each alarm
        bearer_url: URL to get the bearer token (defaults to config value if None)

    Returns:
        bool: True if any alarm meets the condition, False otherwise

    Raises:
        AuthenticationError: If authentication fails
        DataFetchError: If the API request fails
    """
    # Use default value if parameter is not provided
    bearer_url = bearer_url or bearer_req_url

    try:
        return api.trigger_email_condition(
            condition=condition,
            bearer_url=bearer_url,
            client_id=api_client_id,
            client_secret=api_client_secret
        )
    except (AuthenticationError, DataFetchError) as e:
        # Log the error with detailed information
        logger.error(f"Error in trigger_email_condition: {str(e)}")
        logger.error(f"Error type: {type(e).__name__}")
        logger.error(f"Bearer URL: {bearer_url}")
        logger.error(f"Condition: {condition}")

        # Re-raise the exception to allow proper handling by the caller
        raise


def make_api_request(token, request_url):
    """
    Make an API request with the provided token and URL.

    Args:
        token: Bearer token for authentication
        request_url: URL to make the request to

    Returns:
        dict: The JSON response data if successful

    Raises:
        DataFetchError: If the request fails
    """
    try:
        return api.make_api_request(
            token=token,
            request_url=request_url
        )
    except DataFetchError as e:
        # Log the error with detailed information
        logger.error(f"Error making API request to {request_url}: {str(e)}")
        logger.error(f"Error type: {type(e).__name__}")

        # Re-raise the exception to allow proper handling by the caller
        raise


def api_filter(bearer_url=None, api_req_url=None, severity_levels=None, status_filter=None):
    """
    Filter API data to extract relevant alarm information.

    Args:
        bearer_url: URL to get the bearer token (defaults to config value if None)
        api_req_url: URL to make the API request (defaults to config value if None)
        severity_levels: List of severity levels to include (e.g., ["medium", "high"])
                        If None, defaults to DEFAULT_SEVERITY_LEVELS
        status_filter: Status to filter by (defaults to DEFAULT_STATUS_FILTER if None)

    Returns:
        List of filtered events

    Raises:
        AuthenticationError: If authentication fails
        DataFetchError: If the API request fails
    """
    # Use default values if parameters are not provided
    bearer_url = bearer_url or bearer_req_url
    status_filter = status_filter or DEFAULT_STATUS_FILTER
    severity_levels = severity_levels or DEFAULT_SEVERITY_LEVELS

    try:
        return api.api_filter(
            bearer_url=bearer_url,
            api_req_url=api_req_url,
            severity_levels=severity_levels,
            status_filter=status_filter,
            client_id=api_client_id,
            client_secret=api_client_secret
        )
    except (AuthenticationError, DataFetchError) as e:
        # Log the error with detailed information
        logger.error(f"Error in api_filter: {str(e)}")
        logger.error(f"Error type: {type(e).__name__}")
        logger.error(f"Bearer URL: {bearer_url}")
        logger.error(f"API URL: {api_req_url}")
        logger.error(f"Severity levels: {severity_levels}")
        logger.error(f"Status filter: {status_filter}")

        # Re-raise the exception to allow proper handling by the caller
        raise


def update_config_subject(new_subject):
    """
    Update the email subject in the configuration file.

    Args:
        new_subject: New subject string
    """
    api.update_config_subject(new_subject)


def count_severity(severity_levels=None, status_filter=None):
    """
    Count the number of alarms for each severity level.

    Args:
        severity_levels: List of severity levels to count (defaults to DEFAULT_SEVERITY_LEVELS if None)
        status_filter: Status to filter by (defaults to DEFAULT_STATUS_FILTER if None)

    Returns:
        List of counts for each severity level

    Raises:
        AuthenticationError: If authentication fails
        DataFetchError: If the API request fails
    """
    # Use default values if parameters are not provided
    status_filter = status_filter or DEFAULT_STATUS_FILTER
    severity_levels = severity_levels or DEFAULT_SEVERITY_LEVELS

    try:
        return api.count_severity(
            severity_levels=severity_levels,
            status_filter=status_filter,
            client_id=api_client_id,
            client_secret=api_client_secret
        )
    except (AuthenticationError, DataFetchError) as e:
        # Log the error with detailed information
        logger.error(f"Error in count_severity: {str(e)}")
        logger.error(f"Error type: {type(e).__name__}")
        logger.error(f"Severity levels: {severity_levels}")
        logger.error(f"Status filter: {status_filter}")

        # Re-raise the exception to allow proper handling by the caller
        raise


def get_bearer_token(token_url=None):
    """
    Get a bearer token for API authentication.

    Args:
        token_url: URL to get the bearer token. If None, uses the default URL from config.

    Returns:
        str: The access token if successful

    Raises:
        AuthenticationError: If authentication fails
    """
    # Use default value if parameter is not provided
    token_url = token_url or bearer_req_url

    try:
        return api.get_bearer_token(
            bearer_url=token_url,
            client_id=api_client_id,
            client_secret=api_client_secret
        )
    except AuthenticationError as e:
        # Log the error with detailed information
        logger.error(f"Authentication error when getting bearer token: {str(e)}")
        logger.error(f"Error type: {type(e).__name__}")
        logger.error(f"API URL: {token_url}")
        logger.error(f"API username: {api_client_id}")

        # Re-raise the exception to allow proper handling by the caller
        raise
