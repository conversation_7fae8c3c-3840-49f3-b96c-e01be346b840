# Astro Automation

## Project Overview

Astro Automation is a comprehensive security monitoring and notification system designed to automate the detection, analysis, and reporting of security alarms from the Astro API. The system continuously monitors for security incidents, processes them using AI-powered analysis, and delivers detailed notifications to security teams.

## Key Features

- **Real-time Security Monitoring**: Continuously polls the Astro API for new security alarms at configurable intervals (default: 3 minutes)
- **Severity-based Filtering**: Configurable filtering of alarms based on severity levels (low, medium, high)
- **AI-powered Analysis**: Integration with Google's Gemini AI API to generate intelligent security recommendations
- **Automated Email Notifications**: Customizable email notifications with detailed alarm information and AI recommendations
- **Flexible Deployment Options**: Run as a standalone script, scheduled task, or Windows service
- **Comprehensive Logging**: Detailed logging with automatic rotation and archiving
- **Secure Credential Management**: Environment variable-based credential storage with fallback options
- **Profile-based Configuration**: Support for multiple monitoring profiles with different settings

## System Architecture

The Astro Automation system is organized into several key components:

### Core Components

1. **Profilers**: Profile-specific modules that define monitoring configurations
2. **API Integration**: Modules for interacting with the Astro security API
3. **Logic Processing**: Business logic for alarm detection and processing
4. **Export Methods**: Modules for generating notifications (email, documents, etc.)
5. **Utilities**: Helper functions for configuration, logging, and other common tasks

### Deployment Options

1. **Single Execution Mode**: Run once and exit (via `app.py`)
2. **Monitoring Mode**: Continuous polling in a console window (via `app.py --monitor`)
3. **Windows Service**: Background service without a console window (via `service.py`)

## Configuration System

The configuration system uses JSON files organized by function:

- `app.json`: Main application settings
- `email.json`: Email server and template settings
- `logging.json`: Logging settings
- `api.json`: API connection settings
- `profiles/*.json`: Profile-specific configurations

Environment variables are used for sensitive credentials using the `${VARIABLE_NAME}` syntax:

```
ASTRO_API_USERNAME=your_api_username
ASTRO_API_SECRET=your_api_secret
ASTRO_SMTP_EMAIL=<EMAIL>
ASTRO_SMTP_PASSWORD=your_app_password
ASTRO_EMAIL_RECIPIENT=<EMAIL>
GEMINI_API_KEY=your_gemini_api_key
```

## Workflow Process

1. **Initialization**: Load configuration and set up logging
2. **API Authentication**: Authenticate with the Astro API
3. **Alarm Detection**: Poll the API for new security alarms
4. **Data Processing**: Filter and process alarm data
5. **AI Analysis**: Send alarm data to Gemini AI for recommendations
6. **Notification Generation**: Create email notifications with alarm details and recommendations
7. **Notification Delivery**: Send notifications to configured recipients
8. **Cleanup**: Manage logs and temporary files

## Email Notification System

The email notification system provides detailed information about detected security alarms:

- **Alarm Details**: Source IP, username, timestamp, severity, and event classification
- **AI Recommendations**: Security recommendations generated by Gemini AI
- **Customizable Templates**: HTML templates for consistent and professional notifications

## Gemini AI Integration

The system integrates with Google's Gemini AI to provide intelligent security recommendations:

1. **Alarm Analysis**: Send alarm details to Gemini AI for analysis
2. **Recommendation Generation**: Receive tailored security recommendations
3. **Notification Enhancement**: Include recommendations in email notifications

## Real-time Monitoring

The real-time monitoring system provides continuous security monitoring:

- **Configurable Polling**: Adjust polling intervals based on security requirements
- **Error Handling**: Automatic recovery from temporary failures
- **Resource Management**: Efficient use of system resources

## Project Structure

```
Astro_Automation/
├── config/                  # Configuration files
│   ├── app.json             # Main application settings
│   ├── email.json           # Email settings
│   ├── logging.json         # Logging settings
│   ├── api.json             # API settings
│   ├── profiles/            # Profile configurations
│   └── templates/           # Configuration templates
├── src/                     # Source code
│   ├── export/              # Export functionality
│   │   ├── email_server.py  # Email notifications
│   │   └── export_*.py      # Other export methods
│   ├── logic/               # Business logic
│   │   ├── api_method.py    # API methods
│   │   └── recommendation.py # AI recommendations
│   ├── profilers/           # Profiler modules
│   │   ├── base_profiler.py # Base profiler class
│   │   └── profiler_3.py    # UMWT profiler
│   ├── utils/               # Utility functions
│   │   ├── config.py        # Configuration loading
│   │   └── logger.py        # Logging functionality
│   ├── main.py              # Main entry point
│   └── monitor.py           # Monitoring functionality
├── resources/               # Static resources
│   ├── templates/           # Email templates
│   └── hash/                # Hash storage
├── logs/                    # Log files
├── app.py                   # Application entry point
├── service.py               # Windows service
└── requirements.txt         # Dependencies
```

## Getting Started

### Prerequisites

- Python 3.6 or higher
- Required Python packages (see `requirements.txt`)
- Access to the Astro API
- (Optional) Gemini AI API key

### Installation

1. Clone the repository
2. Install dependencies:
   ```
   pip install -r requirements.txt
   ```
3. Create a `.env` file with your credentials
4. Configure your profiles in `config/profiles/`

### Running the Application

#### Single Execution

```
python app.py
```

#### Monitoring Mode

```
python app.py --monitor --interval 180
```

#### As a Windows Service

```
# Install the service
python service.py install

# Start the service
python service.py start
```

## Configuration Guide

### Profile Configuration

Create a profile configuration in `config/profiles/your_profile.json`:

```json
{
    "Profile": {
        "name": "Your Profile",
        "description": "Your Profile Description"
    },
    "API_Credentials": {
        "api_username": "${ASTRO_API_USERNAME}",
        "api_secret": "${ASTRO_API_SECRET}"
    },
    "API_URL": {
        "bearer_request_url": "https://api.example.com/auth",
        "api_request_url": "https://api.example.com/alarms"
    },
    "Email": {
        "sender": "${ASTRO_SMTP_EMAIL}",
        "receivers": ["${ASTRO_EMAIL_RECIPIENT}"],
        "subject": "Security Alert",
        "use_template_in_body": true,
        "use_gemini_ai": true
    }
}
```

### Email Configuration

Configure email settings in `config/email.json`:

```json
{
    "SMTP": {
        "server": "smtp.gmail.com",
        "port": 587,
        "email": "${ASTRO_SMTP_EMAIL}",
        "password": "${ASTRO_SMTP_PASSWORD}"
    },
    "Template": {
        "use_template": true,
        "template_path": "resources/templates/email_template.html"
    }
}
```

## Maintenance and Troubleshooting

### Log Management

Logs are stored in the `logs/` directory:
- `application.log`: Current application log
- `error.log`: Current error log
- `daily/YYYY-MM-DD/`: Daily logs
- `archive/`: Archived old logs

### Common Issues

1. **API Connection Failures**: Check API credentials and network connectivity
2. **Email Sending Failures**: Verify SMTP settings and credentials
3. **Missing Recommendations**: Ensure Gemini API key is correctly configured

## Future Enhancements

- Web dashboard for monitoring and configuration
- Additional notification channels (SMS, Slack, Teams)
- Enhanced AI analysis with multiple models
- Automated response actions for common security incidents
