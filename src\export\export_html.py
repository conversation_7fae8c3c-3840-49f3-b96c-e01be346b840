# Standard library imports
import html
import os

# Third-party imports
from jinja2 import Environment, FileSystemLoader

# Project-specific imports
from src.utils.logger import logger


def create_html_file(data):
    """
    Create an HTML file from data using a template.

    Args:
        data: List of dictionaries containing the data to display
    """
    logger.info(f"Creating HTML file with {len(data)} items")

    # Configure Jinja2 template environment
    template_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "resources", "templates")
    env = Environment(loader=FileSystemLoader(template_dir))
    template = env.get_template("template.html")

    # Prepare data for template rendering
    table_data = []
    for item in data:
        row = {key: html.escape(str(value)) for key, value in item.items()}
        table_data.append(row)

    # Render the template with data
    html_table = template.render(data=table_data)

    # Create HTML file
    output_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "resources", "exports")
    os.makedirs(output_dir, exist_ok=True)
    output_path = os.path.join(output_dir, "output.html")
    with open(output_path, "w") as file:
        file.write(html_table)

    logger.info(f"HTML file saved to {output_path}")