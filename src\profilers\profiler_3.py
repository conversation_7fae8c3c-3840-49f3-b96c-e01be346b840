"""
Profiler module for UMWT configuration.

This module provides a clean interface to the BaseProfiler class,
eliminating the need for global variables and providing proper
property access to the underlying profiler instance.
"""
import os
from typing import Dict, Any, List, Tuple

# Import the base profiler class
from src.profilers.base_profiler import BaseProfiler

# Import the logger for proper logging
from src.utils.logger import logger
from src.utils.utility import ensure_directory_exists

# Get the project root directory (3 levels up from this file)
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))

# Define the standard config path
config_file_path = os.path.join(project_root, "config", "profiles", "config_umwt.json")

# Define possible alternative paths in order of preference
config_paths = [
    # Primary path (new structure)
    config_file_path,
    # Old structure path
    os.path.join(project_root, "configs", "profiles", "config_umwt.json"),
    # Alternative filename in new structure
    os.path.join(project_root, "config", "profiles", "umwt.json"),
    # Alternative filename in old structure
    os.path.join(project_root, "configs", "profiles", "umwt.json"),
]

# Find the first path that exists
for path in config_paths:
    if os.path.exists(path):
        config_file_path = path
        logger.info(f"Found config file at: {config_file_path}")
        break
else:
    # If no config file is found, log the error
    logger.error("Could not find config file at any expected location:")
    for path in config_paths:
        logger.error(f"  Tried: {path}")

    # Ensure the config directory exists
    config_dir = os.path.join(project_root, "config", "profiles")
    ensure_directory_exists(config_dir)

    # Continue with the default path, which will likely fail later
    logger.warning(f"Using default path which may not exist: {config_file_path}")

# Log the path being used
logger.info(f"Using config file: {config_file_path}")

_profiler_instance = BaseProfiler(config_file_path)

# Variables for storing cached data
data3 = None
md5_hash3 = None
crc_hash3 = None


def initialize_data() -> Tuple[List[Dict[str, Any]], str, int]:
    """
    Initialize data by calling the API and calculating hashes.

    Returns:
        Tuple containing (data, md5_hash, crc_hash)
    """
    # Use the profiler instance to initialize data
    _profiler_instance.initialize_data()

    # Return the data and hashes
    return _profiler_instance.data, _profiler_instance.md5_hash, _profiler_instance.crc_hash


def get_instance() -> BaseProfiler:
    """
    Get the profiler instance.

    Returns:
        The BaseProfiler instance
    """
    return _profiler_instance


# For backward compatibility, provide property access to all profiler attributes
# This allows code to still access attributes like profiler_3.sender without
# directly exposing global variables

def __getattr__(name: str) -> Any:
    """
    Dynamic attribute access for backward compatibility.

    This special method is called when an attribute is not found in the module.
    It allows us to delegate attribute access to the profiler instance.

    Args:
        name: The name of the attribute to access

    Returns:
        The value of the attribute from the profiler instance

    Raises:
        AttributeError: If the attribute doesn't exist in the profiler instance
    """
    if hasattr(_profiler_instance, name):
        return getattr(_profiler_instance, name)
    raise AttributeError(f"Module {__name__} has no attribute {name}")
