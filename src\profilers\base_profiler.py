# Standard library imports
from typing import Dict, Any, List

# Project-specific imports
from src.logic import api_method
from src.logic import hash
from src.utils import credential_manager


class BaseProfiler:
    """
    Base class for all profilers. Provides common functionality and configuration loading.
    Individual profiler modules should inherit from this class.
    """

    def __init__(self, config_file_path: str):
        """
        Initialize the profiler with configuration from the specified file.

        Args:
            config_file_path: Path to the configuration file (JSON)
        """
        self.config_file_path = config_file_path
        self.config = self._load_config()

        # Initialize all configuration properties
        self._initialize_config()

        # Initialize data and hash values as None
        self.data = None
        self.md5_hash = None
        self.crc_hash = None

    def _load_config(self) -> Dict[str, Any]:
        """
        Load configuration from the JSON file and update it with credentials from environment variables.

        Returns:
            Dict containing the configuration with secure credentials
        """
        # Use the credential manager to load the config with secure credentials
        return credential_manager.load_config_with_credentials(self.config_file_path)

    def _initialize_config(self) -> None:
        """
        Initialize all configuration properties from the loaded config.
        """
        # Email configuration
        self.sender = self.config["Email"]["sender"]
        self.receiver = [item.strip() for item in self.config["Email"]["receivers"]]
        self.subject = self.config["Email"]["subject"]
        self.message = self.config["Email"]["message"]

        # API configuration
        self.bearer_req_url = self.config['API_URL']['bearer_request_url']
        self.api_request_url = self.config['API_URL']['api_request_url']
        self.api_client_id = self.config['API_Credentials']['api_username']
        self.api_client_secret = self.config['API_Credentials']['api_secret']

        # SMTP configuration
        self.smtp_server = self.config["SMTP"]["server"]
        self.smtp_port = self.config["SMTP"]["port"]
        self.smtp_username = self.config["SMTP"]["email"]
        self.smtp_password = self.config["SMTP"]["app_pass"]

        # Attachment configuration
        self.attachment_file1 = self.config["Attachments"]["file1"]
        self.attachment_file2 = self.config["Attachments"]["file2"]
        self.attachment_file3 = self.config["Attachments"]["file3"]

        self.attachments = []
        attachment_keys = self.config["Attachments"].keys()
        for key in attachment_keys:
            attachment_path = self.config["Attachments"][key]
            self.attachments.append(attachment_path)

        # Directory configuration
        self.directory = self.config["Directory"]["directory"]
        self.hash_directory = self.config["Directory"]["hash_directory"]

        # Document configuration
        self.table_width_ratio = [int(val) for val in self.config["Docx"]["table_width_ratio"]]
        self.table_total_width = self.config["Docx"]["table_total_width"]
        self.document_title = self.config["Docx"]["document_title"]
        self.document_title_font = self.config["Docx"]["document_title_font"]
        self.document_title_size = self.config["Docx"]["document_title_size"]
        self.content_font = self.config["Docx"]["content_font"]
        self.content_size = self.config["Docx"]["content_size"]

        # Trigger configuration
        self.send_docx = self.config["Triggers"]["send_docx"]
        self.send_html = self.config["Triggers"]["send_html"]
        self.send_xlsx = self.config["Triggers"]["send_xlsx"]
        self.send_csv = self.config["Triggers"]["send_csv"]
        self.send_image = self.config["Triggers"]["send_image"]
        self.send_email = self.config["Triggers"]["send_email"]
        self.delete_local = self.config["Triggers"]["delete_local"]
        self.upload_gdrive = self.config["Triggers"]["upload_gdrive"]
        self.trigger_email_condition = self.config["Triggers"]["trigger_email_condition"]

        # Always use medium and high severity levels as per user preference
        self.severity_levels = ["medium", "high"]

        # Image configuration
        email_image_config = self.config["Email_Image"]
        self.image_width = email_image_config["image_width"]
        self.image_height = email_image_config["image_height"]
        self.image_background_color = tuple(email_image_config["image_background_color"])
        self.image_title_color = tuple(email_image_config["image_title_color"])
        self.image_text_color = tuple(email_image_config["image_text_color"])
        self.image_footer_color = tuple(email_image_config["image_footer_color"])
        self.image_font = email_image_config["image_font"]
        self.image_font_size = email_image_config["image_font_size"]
        self.image_title = email_image_config["image_title"]
        self.image_text_lines = email_image_config["image_text_lines"]
        self.image_footer = email_image_config["image_footer"]
        self.image_output_path = email_image_config["image_output_path"]

        # Google Drive API configuration
        gdrive_api_config = self.config["Google_Drive_API"]
        self.gdrive_client_secret = gdrive_api_config["gdrive_client_secret"]
        self.gdrive_directory = gdrive_api_config["gdrive_directory"]
        self.gdrive_client_config_backend = gdrive_api_config["gdrive_client_config_backend"]
        self.gdrive_save_credentials = gdrive_api_config["gdrive_save_credentials"]
        self.gdrive_save_credentials_backend = gdrive_api_config["gdrive_save_credentials_backend"]
        self.gdrive_save_credentials_file = gdrive_api_config["gdrive_save_credentials_file"]
        self.gdrive_get_refresh_token = gdrive_api_config["gdrive_get_refresh_token"]
        self.gdrive_client_id = gdrive_api_config["gdrive_client_id"]
        self.gdrive_oauth_scope = gdrive_api_config["gdrive_oauth_scope"]
        self.gdrive_upload_url = gdrive_api_config["gdrive_upload_url"]
        self.gdrive_api_key = gdrive_api_config["gdrive_api_key"]

    def initialize_data(self) -> None:
        """
        Initialize data by calling the API and calculating hashes.
        """
        # Data pulled from API with configurable severity levels
        self.data = api_method.api_filter(
            self.bearer_req_url,
            self.api_request_url,
            severity_levels=self.severity_levels
        )

        # Hashed data (MD5 or CRC32)
        self.md5_hash = hash.calculate_md5_hash(self.data)
        self.crc_hash = hash.calculate_crc32_hash(self.data)

    def get_data(self) -> List[Dict[str, Any]]:
        """
        Get the data from the API. Initialize if not already done.

        Returns:
            List of dictionaries containing the data
        """
        if self.data is None:
            self.initialize_data()
        return self.data

    def get_md5_hash(self) -> str:
        """
        Get the MD5 hash of the data. Initialize if not already done.

        Returns:
            MD5 hash as a string
        """
        if self.md5_hash is None:
            self.initialize_data()
        return self.md5_hash

    def get_crc_hash(self) -> int:
        """
        Get the CRC32 hash of the data. Initialize if not already done.

        Returns:
            CRC32 hash as an integer
        """
        if self.crc_hash is None:
            self.initialize_data()
        return self.crc_hash
