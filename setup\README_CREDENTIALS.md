# Secure Credential Management

This document explains how to use the new secure credential management system in the Astro Automation project.

## Why Secure Credential Management?

Storing sensitive credentials like API keys, passwords, and secrets directly in configuration files is a security risk. These files might be accidentally committed to version control, shared with unauthorized users, or exposed in other ways.

The new credential management system addresses this by:

1. Storing sensitive credentials as environment variables
2. Providing a fallback to configuration files when environment variables are not available
3. Offering optional encryption for sensitive data

## Setting Up Secure Credentials

### Option 1: Using the Setup Script

The easiest way to set up your credentials is to use the provided setup script:

```bash
python setup_credentials.py
```

This script will:
1. Read your existing configuration files
2. Prompt you for credentials that should be stored securely
3. Create a `.env` file with these credentials

### Option 2: Manual Setup

If you prefer to set up your credentials manually, you can create a `.env` file with the following format:

```
ASTRO_API_USERNAME=your_api_username
ASTRO_API_SECRET=your_api_secret
ASTRO_SMTP_EMAIL=your_smtp_email
ASTRO_SMTP_PASSWORD=your_smtp_password
ASTRO_GDRIVE_CLIENT_ID=your_gdrive_client_id
ASTRO_GDRIVE_CLIENT_SECRET=your_gdrive_client_secret
ASTRO_GDRIVE_API_KEY=your_gdrive_api_key
ASTRO_ENCRYPTION_KEY=your_encryption_key
```

### Option 3: System Environment Variables

For production environments, it's recommended to set these variables as system environment variables rather than using a `.env` file.

## How It Works

1. When the application starts, it loads environment variables from the `.env` file (if available)
2. The credential manager checks for credentials in environment variables
3. If a credential is not found in environment variables, it falls back to the value in the configuration file
4. If encryption is enabled (by setting the `ASTRO_ENCRYPTION_KEY` environment variable), sensitive data can be encrypted and decrypted

## Security Best Practices

1. **Never commit your `.env` file to version control**
   - Add `.env` to your `.gitignore` file

2. **Use strong, unique passwords for all credentials**
   - Consider using a password manager to generate and store these

3. **Regularly rotate your credentials**
   - Update your API keys, passwords, and secrets periodically

4. **Limit access to your credentials**
   - Only share credentials with those who need them

5. **Use different credentials for development and production**
   - Keep your production credentials separate from your development credentials

## Troubleshooting

If you encounter issues with the credential management system:

1. **Check if your `.env` file is in the correct location**
   - It should be in the root directory of the project

2. **Verify that your environment variables are set correctly**
   - Use `echo $ASTRO_API_USERNAME` (Linux/Mac) or `echo %ASTRO_API_USERNAME%` (Windows) to check

3. **Look for error messages in the console**
   - The credential manager will print warnings if it can't find credentials

4. **Try running the setup script again**
   - `python setup_credentials.py`

If you continue to have issues, please contact the project maintainer.
