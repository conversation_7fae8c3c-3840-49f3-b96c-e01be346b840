"""
Utility module containing common functions and utilities used throughout the application.
This module consolidates utility functions from utility.py and utility_function.py
to provide a single, consistent interface for common operations.
"""

# Standard library imports
import os
import re
import traceback
from datetime import datetime

# Import the centralized logger
from src.utils.logger import info, logger


def convert_epoch_to_date(epoch_timestamp):
    """
    Convert an epoch timestamp to a formatted date string.

    Args:
        epoch_timestamp: Epoch timestamp in milliseconds

    Returns:
        str: Formatted date string or "Invalid timestamp" if conversion fails
    """
    try:
        epoch_time = int(epoch_timestamp)
        epoch_time = epoch_time / 1000
        converted_time = datetime.fromtimestamp(epoch_time)
        return converted_time.strftime("%a, %b %d %Y, %I:%M:%S %p")
    except (ValueError, TypeError, OverflowError) as e:
        logger.error(f"Error converting timestamp: {e}")
        return "Invalid timestamp"


def delete_temp_files(profiler_name: object) -> None:
    """
    Delete all files in the profiler's directory.

    Args:
        profiler_name: The profiler object containing the directory attribute

    Returns:
        None
    """
    profiler = profiler_name
    try:
        for filename in os.listdir(profiler.directory):
            path = os.path.join(profiler.directory, filename)
            if os.path.isfile(path):  # Check if it is a file
                os.remove(path)
                logger.debug(f"Deleted file: {path}")
        logger.info(f"Cleaned up temporary files in {profiler.directory}")
    except Exception as e:
        logger.error(f"Error deleting temporary files: {e}")
        logger.debug(traceback.format_exc())


def extract_number_from_string(string: str) -> int:
    """
    Extract a number from a string matching the pattern 'profiler_X'.

    Args:
        string: The string to extract the number from

    Returns:
        int: The extracted number, or None if no match is found
    """
    pattern = r'profiler_(\d+)'
    match = re.search(pattern, string)

    if match:
        extracted_number = int(match.group(1))
        return extracted_number
    else:
        return None


def start_logging_program_execution():
    """Start logging for a new program execution session."""
    info("\n\n%s\n", "=" * 80)
    info("Starting program execution")


def stop_logging_program_execution():
    """End logging for the current program execution session."""
    info("Program execution completed")
    info("%s\n\n\n", "=" * 80)


def ensure_directory_exists(directory_path):
    """
    Ensure that a directory exists, creating it if necessary.

    Args:
        directory_path: Path to the directory to check/create

    Returns:
        bool: True if the directory exists or was created, False otherwise
    """
    try:
        if not os.path.exists(directory_path):
            os.makedirs(directory_path, exist_ok=True)
            logger.info(f"Created directory: {directory_path}")
        return True
    except Exception as e:
        logger.error(f"Error creating directory {directory_path}: {e}")
        logger.debug(traceback.format_exc())
        return False

