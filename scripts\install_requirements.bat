@echo off
echo Installing required dependencies for Astro Automation...
echo.

REM Check if Python is available
where python >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Python not found. Please make sure Python is installed and in your PATH.
    pause
    exit /b 1
)

REM Install required packages
echo Installing pywin32 (required for Windows service)...
python -m pip install pywin32

echo.
echo Dependencies installed successfully.
echo.
pause
