"""
Gmail OAuth2 authentication module.

This module provides functionality for authenticating with Gmail using OAuth2.
"""
import os
import base64
import json
from pathlib import Path
from typing import Dict, Any, Optional

# Third-party imports
import requests
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from google.auth.transport.requests import Request

# Project-specific imports
from src.utils.logger import logger

# Constants
GMAIL_TOKEN_FILE = "gmail_token.json"
GMAIL_CREDENTIALS_FILE = "gmail_credentials.json"
GMAIL_SCOPES = ["https://mail.google.com/"]


def get_oauth2_string(user_email: str) -> Optional[str]:
    """
    Get the OAuth2 string for Gmail SMTP authentication.

    Args:
        user_email: The Gmail email address to authenticate

    Returns:
        OAuth2 string for SMTP authentication, or None if authentication fails
    """
    try:
        # Get the access token
        access_token = get_access_token(user_email)
        if not access_token:
            logger.error("Failed to get access token for Gmail OAuth2 authentication")
            return None

        # Create the OAuth2 string
        auth_string = f"user={user_email}\1auth=Bearer {access_token}\1\1"
        b64_auth_string = base64.b64encode(auth_string.encode()).decode()
        return b64_auth_string
    except Exception as e:
        logger.error(f"Error getting OAuth2 string: {str(e)}")
        return None


def get_access_token(user_email: str) -> Optional[str]:
    """
    Get the access token for Gmail OAuth2 authentication.

    Args:
        user_email: The Gmail email address to authenticate

    Returns:
        Access token for Gmail OAuth2 authentication, or None if authentication fails
    """
    try:
        # Check if we have a token file
        token_path = Path(GMAIL_TOKEN_FILE)
        credentials = None

        # Load credentials from token file if it exists
        if token_path.exists():
            try:
                with open(token_path, "r") as token_file:
                    token_data = json.load(token_file)
                    # Check if the token is for the correct user
                    if token_data.get("email") == user_email:
                        credentials = Credentials.from_authorized_user_info(token_data)
            except Exception as e:
                logger.error(f"Error loading token file: {str(e)}")

        # If credentials don't exist or are invalid, get new credentials
        if not credentials or not credentials.valid:
            if credentials and credentials.expired and credentials.refresh_token:
                # Refresh the token if it's expired
                try:
                    credentials.refresh(Request())
                    logger.info("Successfully refreshed Gmail OAuth2 token")
                except Exception as e:
                    logger.error(f"Error refreshing token: {str(e)}")
                    credentials = None
            else:
                # Get new credentials
                credentials = get_new_credentials(user_email)

            # Save the credentials to the token file
            if credentials:
                save_credentials(credentials, user_email)

        # Return the access token
        if credentials:
            return credentials.token
        else:
            logger.error("Failed to get valid credentials for Gmail OAuth2 authentication")
            return None
    except Exception as e:
        logger.error(f"Error getting access token: {str(e)}")
        return None


def get_new_credentials(user_email: str) -> Optional[Credentials]:
    """
    Get new credentials for Gmail OAuth2 authentication.

    Args:
        user_email: The Gmail email address to authenticate

    Returns:
        Credentials object, or None if authentication fails
    """
    try:
        # Check if we have a credentials file
        credentials_path = Path(GMAIL_CREDENTIALS_FILE)
        if not credentials_path.exists():
            logger.error(f"Gmail credentials file not found: {GMAIL_CREDENTIALS_FILE}")
            logger.error("Run setup_gmail_oauth.py to create the credentials file")
            return None

        # Create the flow
        flow = InstalledAppFlow.from_client_secrets_file(
            GMAIL_CREDENTIALS_FILE, GMAIL_SCOPES
        )

        # Run the flow
        credentials = flow.run_local_server(port=0)
        logger.info("Successfully obtained new Gmail OAuth2 credentials")
        return credentials
    except Exception as e:
        logger.error(f"Error getting new credentials: {str(e)}")
        return None


def save_credentials(credentials: Credentials, user_email: str) -> bool:
    """
    Save credentials to the token file.

    Args:
        credentials: Credentials object to save
        user_email: The Gmail email address associated with the credentials

    Returns:
        True if the credentials were saved successfully, False otherwise
    """
    try:
        # Convert credentials to a dictionary
        token_data = json.loads(credentials.to_json())
        # Add the email to the token data
        token_data["email"] = user_email

        # Save the token data to the token file
        with open(GMAIL_TOKEN_FILE, "w") as token_file:
            json.dump(token_data, token_file)
        logger.info(f"Saved Gmail OAuth2 credentials to {GMAIL_TOKEN_FILE}")
        return True
    except Exception as e:
        logger.error(f"Error saving credentials: {str(e)}")
        return False
