# Standard library imports
import os
from datetime import datetime
import time
import traceback
import logging

# Load environment variables from .env file
from src.utils.env_loader import load_env_file
from src.utils.credential_manager import credential_manager

# Import the centralized logger and configuration loader
from src.utils.logger import logger, info, debug
from src.utils.config_loader import get_app_config, get_logging_config

# Load environment variables at startup
load_env_file()

# Load configuration
app_config = get_app_config()
logging_config = get_logging_config()

# Enable debug mode for troubleshooting
debug_mode_config = logging_config.get("Debug", {}).get("debug_mode", "true")
# Convert to string if it's a boolean
debug_mode_str = str(debug_mode_config).lower() if isinstance(debug_mode_config, bool) else debug_mode_config.lower()
DEBUG_MODE = os.environ.get("ASTRO_DEBUG_MODE", debug_mode_str) in ("true", "1", "yes", "y")

# Configure logging level based on debug mode
if DEBUG_MODE:
    logger.set_level(logger.DEBUG)
    # Reduce noise from urllib3
    logging.getLogger("urllib3").setLevel(logging.INFO)
    info("DEBUG MODE ENABLED - Showing detailed logs")
    debug(f"App config loaded: {app_config.keys()}")
    debug(f"Logging config loaded: {logging_config.keys()}")

# Check if critical credentials are available
def check_credentials():
    """Check if critical credentials are available and prompt if missing."""
    critical_credentials = [
        ("API_USERNAME", "API username"),
        ("API_SECRET", "API secret"),
        ("SMTP_EMAIL", "SMTP email"),
        ("SMTP_PASSWORD", "SMTP password")
    ]

    missing_credentials = []
    for env_var, description in critical_credentials:
        if not os.environ.get(f"ASTRO_{env_var}"):
            missing_credentials.append(description)

    if missing_credentials:
        print("\nWARNING: The following critical credentials are missing:")
        for cred in missing_credentials:
            print(f"  - {cred}")
        print("\nYou can set these credentials by running setup_credentials.py")
        print("Attempting to continue with default or prompted values...\n")

        # Try to get credentials interactively
        for env_var, description in critical_credentials:
            if not os.environ.get(f"ASTRO_{env_var}"):
                credential_manager.get_credential(env_var)

# Check credentials before running
check_credentials()

# Project-specific imports
from src.utils import utility
from src.logic import profiler_controller
from src.logic import hash
from src.logic import api_method
from src.export import export_docx
from src.export import export_html
from src.export import export_spreadsheet
from src.export import email_server
from src.logic import api_gdrive

# Import profiler module and API base
from src.profilers import profiler_3
from src.logic.api_base import ApiBase

# Helper function to generate mock data
def generate_mock_data():
    """
    Generate mock alarm data for testing or when API is unavailable.
    Uses the user-preferred severity levels (medium and high only).

    Returns:
        List of mock alarm data dictionaries
    """
    logger.warning(f"Generating mock data with severity levels: {SEVERITY_LEVELS}")

    # Create a mock API instance with debug mode enabled
    mock_api = ApiBase(debug_mode=True)
    mock_api.use_mock_data = True

    # Generate mock data using the API's method
    mock_data = mock_api._generate_mock_data(SEVERITY_LEVELS)

    info(f"Generated {len(mock_data)} mock alarms")
    return mock_data

# Initialize profiler data if needed
if profiler_3.data3 is None:
    profiler_3.data3, profiler_3.md5_hash3, profiler_3.crc_hash3 = profiler_3.initialize_data()


# Define constants for severity levels (always use medium and high as per user preference)
SEVERITY_LEVELS = ["medium", "high"]


def setup_profiler_directories(profiler):
    """
    Set up necessary directories for the profiler.

    Args:
        profiler: The profiler object

    Returns:
        None
    """
    # Check for hash directory
    if not os.path.exists(profiler.hash_directory):
        os.makedirs(profiler.hash_directory, exist_ok=True)
        info(f"Hash directory not found, new directory created at {profiler.hash_directory}")

    # Check for output directory
    if not os.path.exists(profiler.directory):
        os.makedirs(profiler.directory, exist_ok=True)
        info(f"Directory not found, new directory created at {profiler.directory}")


def check_for_alarms(profiler):
    """
    Check if there are any alarms that meet the trigger condition.

    Args:
        profiler: The profiler object

    Returns:
        bool: True if alarms are detected, False otherwise
    """
    try:
        # Check for alarms using the real API
        alarms_detected = api_method.trigger_email_condition(
            profiler.trigger_email_condition,
            profiler.bearer_req_url
        )
        info(f"Alarm detection result: {alarms_detected}")
        return alarms_detected
    except Exception as e:
        logger.error(f"Failed to check for alarms: {str(e)}")
        if DEBUG_MODE:
            logger.warning("DEBUG MODE: Continuing with mock data despite API error")
            logger.debug(f"Exception details: {traceback.format_exc()}")
            return True
        return False


def get_alarm_data(profile):
    """
    Get alarm data from the API or generate mock data if in debug mode.

    Args:
        profile: The profile name

    Returns:
        tuple: (data, hash) or (None, None) if no data could be retrieved
    """
    try:
        # Get real data from the API
        data = profiler_controller.data(profile)

        # Check if we got valid data
        if not data:
            logger.error("No data retrieved from API")
            if DEBUG_MODE:
                # In debug mode, generate mock data
                data = generate_mock_data()
            else:
                return None, None

        # Calculate hash of the data
        data_hash = hash.calculate_md5_hash(data)
        return data, data_hash

    except Exception as e:
        logger.error(f"Failed to retrieve or process data: {str(e)}")
        logger.debug(f"Exception details: {traceback.format_exc()}")

        if DEBUG_MODE:
            # In debug mode, generate mock data
            data = generate_mock_data()
            data_hash = hash.calculate_md5_hash(data)
            return data, data_hash

        return None, None


def process_exports(profiler, data):
    """
    Process and export data in various formats based on profiler settings.

    Args:
        profiler: The profiler object
        data: The data to export

    Returns:
        None
    """
    if profiler.send_docx:
        export_docx.export_to_docx(data, profiler.attachment_file3, profiler)
        info("Created DOCX file")

    if profiler.send_html:
        export_html.create_html_file(data)
        info("Created HTML file")

    if profiler.send_xlsx:
        export_spreadsheet.export_to_excel(data, profiler.attachment_file1, profiler_name=profiler)
        info("Created XLSX file")

    if profiler.send_csv:
        export_spreadsheet.export_to_csv(data, profiler.attachment_file2, profiler_name=profiler)
        info("Created CSV file")


def send_notifications(profiler):
    """
    Send notifications based on profiler settings.

    Args:
        profiler: The profiler object

    Returns:
        None
    """
    if profiler.upload_gdrive:
        api_gdrive.upload_to_gdrive(profiler)
        info("Uploading to Google Drive")

    if profiler.send_email:
        email_server.send_query_to_email(
            profiler.sender,
            profiler.receiver,
            profiler.smtp_password,
            profiler.message,
            profiler.smtp_server,
            profiler.smtp_port,
            profiler.subject,
            profiler_name=profiler
        )
        info("Email sending process initiated")

    if profiler.delete_local:
        utility.delete_temp_files(profiler_name=profiler)
        info("Local files have been deleted")


def process_profiler(profile):
    """
    Process a single profiler.

    Args:
        profile: The profile name

    Returns:
        bool: True if processing was successful, False otherwise
    """
    # Get the profiler object
    if profile == "profiler_3":
        # Use the get_instance method for profiler_3
        profiler = profiler_3.get_instance()
    else:
        # Fallback to globals lookup for other profilers
        profiler = globals().get(profile)

    # Start logging
    info("\n\n" + "=" * 80)
    info("Starting program execution")

    # Get hash file number
    hash_file = utility.extract_number_from_string(profile)
    info(f"\t\t\t{profile}\t\t\t")

    # Log current time
    current_time = datetime.fromtimestamp(time.time())
    info(f"Time: {current_time}")

    # Setup directories
    setup_profiler_directories(profiler)

    # Check for alarms
    alarms_detected = check_for_alarms(profiler)
    if not alarms_detected:
        info("No alarms detected, skipping processing")
        info("Program execution completed")
        info("=" * 80 + "\n\n")
        return False

    info("Alarms detected, proceeding with processing")

    # Get data and calculate hash
    current_data, current_hash = get_alarm_data(profile)
    if current_data is None:
        logger.error("Could not get valid data, skipping processing")
        info("Program execution completed")
        info("=" * 80 + "\n\n")
        return False

    # Record the current hash and check for changes
    hash.compare_hash(os.path.join(profiler.hash_directory, f'hash_md5_record_{hash_file}.txt'), current_hash)

    # Process exports
    process_exports(profiler, current_data)

    # Send notifications
    send_notifications(profiler)

    # Stop logging
    info("Program execution completed")
    info("=" * 80 + "\n\n")
    return True


def manage_log_files():
    """
    Manage log files by archiving old logs and cleaning up the archive.

    Uses configuration values from logging.json for retention periods.
    """
    try:
        from src.utils.log_manager import manage_logs

        # Get log retention settings from configuration
        days_to_keep = logging_config.get("Cleanup", {}).get("days_to_keep", 7)
        max_archive_days = logging_config.get("Cleanup", {}).get("max_archive_days", 30)

        info(f"Managing logs with retention settings: keep {days_to_keep} days, archive max {max_archive_days} days")

        # Archive old logs and delete expired archives
        result = manage_logs(days_to_keep=days_to_keep, max_archive_days=max_archive_days)

        if result["archived_directories"]:
            info(f"Archived {len(result['archived_directories'])} log directories")
            debug(f"Archived directories: {result['archived_directories']}")

        if result["deleted_directories"]:
            info(f"Deleted {result['deleted_directories']} archived log directories")

    except Exception as e:
        logger.error(f"Failed to manage log files: {str(e)}")
        if DEBUG_MODE:
            logger.debug(f"Exception details: {traceback.format_exc()}")


if __name__ == "__main__":
    # Manage log files
    manage_log_files()

    # Get the list of profilers to process
    profiler_list = profiler_controller.profiler_list

    # Process each profiler
    for profile in profiler_list:
        process_profiler(profile)
