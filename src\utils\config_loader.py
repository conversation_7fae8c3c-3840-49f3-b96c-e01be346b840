"""
Configuration loader module that provides a unified interface for loading configuration
from the new centralized configuration system.
"""
import os
import json
import re
import configparser
from typing import Dict, Any

# Import the new configuration system
from src.utils.config import get_config
from src.utils.logger import logger

# Constants for configuration paths
CONFIG_PATHS = {
    'app': ['config/app.json', 'configs/app/app_config.json'],
    'email': ['config/email.json', 'configs/email/email_config.json'],
    'gdrive': ['config/gdrive.json', 'configs/app/gdrive_config.json'],
    'logging': ['config/logging.json', 'configs/logging/logging_config.json'],
    'profiles/umwt': ['config/profiles/config_umwt.json', 'config/profiles/umwt.json',
                      'configs/profiles/config_umwt.json', 'configs/profiles/umwt.json']
}


class ConfigLoader:
    """
    Unified configuration loader that handles different formats.
    Supports JSON and INI files with environment variable substitution.

    This class is maintained for backward compatibility with the old configuration system.
    New code should use the utilities.config module directly.
    """

    # Cache for loaded configurations
    _config_cache = {}

    @staticmethod
    def load(file_path: str, reload: bool = False) -> Dict[str, Any]:
        """
        Load configuration from file, detecting format automatically.
        Supports caching and environment variable substitution.

        Args:
            file_path: Path to the configuration file
            reload: Whether to reload the configuration even if it's cached

        Returns:
            Dict containing the configuration

        Raises:
            ValueError: If the file format is not supported
            FileNotFoundError: If the file does not exist
        """
        logger.debug(f"Loading configuration from {file_path}")

        # Check if we should use the new configuration system
        # Map the file path to a config name
        config_name = None

        # Check if the file path is in our known paths
        for name, paths in CONFIG_PATHS.items():
            if file_path in paths:
                config_name = name
                logger.debug(f"Mapped {file_path} to config name {config_name}")
                break

        # If we found a config name, use the new system
        if config_name:
            logger.debug(f"Using new configuration system for {config_name}")
            return get_config(config_name)

        # If the path starts with config/, extract the config name
        if file_path.startswith('config/'):
            config_name = file_path.replace('config/', '').replace('.json', '')
            logger.debug(f"Extracted config name {config_name} from {file_path}")
            return get_config(config_name)

        # If the file path doesn't match any of the new paths, use the old system
        # Check if the configuration is already cached
        if file_path in ConfigLoader._config_cache and not reload:
            return ConfigLoader._config_cache[file_path]

        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Configuration file not found: {file_path}")

        # Load the configuration based on the file extension
        if file_path.endswith('.json'):
            config_data = ConfigLoader._load_json(file_path)
        elif file_path.endswith('.ini'):
            config_data = ConfigLoader._load_ini(file_path)
        else:
            raise ValueError(f"Unsupported configuration format: {file_path}")

        # Process environment variables in the configuration
        config_data = ConfigLoader._process_env_vars(config_data)

        # Cache the configuration
        ConfigLoader._config_cache[file_path] = config_data

        return config_data

    @staticmethod
    def _load_json(file_path: str) -> Dict[str, Any]:
        """
        Load configuration from a JSON file.

        Args:
            file_path: Path to the JSON file

        Returns:
            Dict containing the configuration
        """
        try:
            with open(file_path, 'r') as f:
                return json.load(f)
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in configuration file {file_path}: {e}")

    @staticmethod
    def _load_ini(file_path: str) -> Dict[str, Dict[str, str]]:
        """
        Load configuration from an INI file.

        Args:
            file_path: Path to the INI file

        Returns:
            Dict containing the configuration
        """
        config = configparser.ConfigParser()
        config.read(file_path)

        # Convert ConfigParser object to dictionary
        result = {}
        for section in config.sections():
            result[section] = {}
            for key, value in config[section].items():
                # Try to parse lists and other data types
                result[section][key] = ConfigLoader._parse_ini_value(value)

        return result

    @staticmethod
    def _parse_ini_value(value: str) -> Any:
        """
        Parse a string value from an INI file into the appropriate type.

        Args:
            value: String value from INI file

        Returns:
            Parsed value (could be string, int, float, bool, or list)
        """
        # Check if it's a comma-separated list
        if ',' in value and not (value.startswith('"') or value.startswith("'")):
            return [ConfigLoader._parse_ini_value(item.strip()) for item in value.split(',')]

        # Try to convert to boolean
        if value.lower() in ('true', 'yes', 'on'):
            return True
        if value.lower() in ('false', 'no', 'off'):
            return False

        # Try to convert to number
        try:
            if '.' in value:
                return float(value)
            else:
                return int(value)
        except ValueError:
            # It's a string
            return value

    @staticmethod
    def _process_env_vars(config_data: Any) -> Any:
        """
        Process environment variables in configuration values.

        Args:
            config_data: Configuration data to process

        Returns:
            Processed configuration data
        """
        if isinstance(config_data, dict):
            return {k: ConfigLoader._process_env_vars(v) for k, v in config_data.items()}
        elif isinstance(config_data, list):
            return [ConfigLoader._process_env_vars(item) for item in config_data]
        elif isinstance(config_data, str):
            return ConfigLoader._replace_env_vars(config_data)
        else:
            return config_data

    @staticmethod
    def _replace_env_vars(value: str) -> str:
        """
        Replace environment variables in a string.

        Args:
            value: String value to process

        Returns:
            Processed string value
        """
        # Find all environment variables in the string
        pattern = r'\${([A-Za-z0-9_]+)}'
        matches = re.findall(pattern, value)

        # Replace each environment variable with its value
        result = value
        for match in matches:
            # Try with the exact name first
            env_value = os.environ.get(match)

            # If not found, try with ASTRO_ prefix
            if env_value is None and not match.startswith('ASTRO_'):
                env_value = os.environ.get(f'ASTRO_{match}')

            if env_value is not None:
                # Log the substitution but mask sensitive values
                is_sensitive = any(keyword in match.lower() for keyword in
                                  ['password', 'secret', 'key', 'token', 'auth'])

                if is_sensitive:
                    logger.debug(f"Substituting environment variable ${{{match}}} with masked value")
                else:
                    logger.debug(f"Substituting environment variable ${{{match}}} with value: {env_value}")

                result = result.replace(f'${{{match}}}', env_value)
            else:
                logger.warning(f"Environment variable ${{{match}}} not found")

        return result

    @staticmethod
    def get_app_config() -> Dict[str, Any]:
        """
        Get the application configuration.

        Returns:
            Dict containing the application configuration
        """
        return get_config('app')

    @staticmethod
    def get_email_config() -> Dict[str, Any]:
        """
        Get the email configuration.

        Returns:
            Dict containing the email configuration
        """
        return get_config('email')

    @staticmethod
    def get_gdrive_config() -> Dict[str, Any]:
        """
        Get the Google Drive configuration.

        Returns:
            Dict containing the Google Drive configuration
        """
        return get_config('gdrive')

    @staticmethod
    def get_logging_config() -> Dict[str, Any]:
        """
        Get the logging configuration.

        Returns:
            Dict containing the logging configuration
        """
        return get_config('logging')


# Create convenience functions
get_app_config = ConfigLoader.get_app_config
get_email_config = ConfigLoader.get_email_config
get_gdrive_config = ConfigLoader.get_gdrive_config
get_logging_config = ConfigLoader.get_logging_config
