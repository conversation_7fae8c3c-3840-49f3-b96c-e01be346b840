{"Profile": {"name": "<PERSON><PERSON><PERSON>", "description": "Default Security Monitoring Profile"}, "Email": {"sender": "${ASTRO_SMTP_EMAIL}", "receivers": ["<EMAIL>"], "subject": "Security Alert: Default Monitoring", "message": "Please review the attached security alerts.", "use_template_in_body": true, "attach_files": false, "use_gemini_ai": true}, "API_URL": {"bearer_request_url": "https://api.example.com/auth", "api_request_url": "https://api.example.com/alerts"}, "API_Credentials": {"api_username": "${ASTRO_API_USERNAME}", "api_secret": "${ASTRO_API_SECRET}"}, "SMTP": {"server": "smtp.gmail.com", "port": 587, "email": "${ASTRO_SMTP_EMAIL}", "app_pass": "${ASTRO_SMTP_PASSWORD}"}, "Attachments": {"file1": "resources/exports/default_report.xlsx", "file2": "resources/exports/default_report.csv", "file3": "resources/exports/default_report.docx"}, "Directory": {"directory": "resources/exports", "hash_directory": "data/hash"}, "Docx": {"table_width_ratio": [1, 2, 1, 3], "table_total_width": 10000, "document_title": "Security Alert Report", "document_title_font": "<PERSON><PERSON>", "document_title_size": 14, "content_font": "<PERSON><PERSON>", "content_size": 10}, "Triggers": {"send_docx": true, "send_html": true, "send_xlsx": true, "send_csv": false, "send_image": false, "send_email": true, "delete_local": false, "upload_gdrive": false, "trigger_email_condition": "Severity in ['medium', 'high']", "severity_levels": ["medium", "high"]}, "Email_Image": {"image_width": 800, "image_height": 600, "image_background_color": [255, 255, 255], "image_title_color": [0, 0, 0], "image_text_color": [50, 50, 50], "image_footer_color": [100, 100, 100], "image_font": "<PERSON><PERSON>", "image_font_size": 12, "image_title": "Security <PERSON><PERSON>", "image_text_lines": ["Low Severity Alerts: ", "Medium Severity Alerts: ", "High Severity Alerts: "], "image_footer": "Generated by Astro Automation", "image_output_path": "resources/exports/alert_summary.png"}}