# Environment variables
.env

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
venv/
.venv/

# Logs
logs/daily/
logs/archive/
logs/*.log
logs_stash/

# Temporary files
resources/temp/
resources/exports/
resources/hash/

# IDE files
.idea/
.vscode/
/shelf/
/workspace.xml
*.swp
*.swo
.DS_Store

# Credentials
credentials.txt
token.json
token.pickle

# Generated files
*.docx
*.xlsx
*.csv
*.html
*.pdf
*.png
*.jpg
*.jpeg
*.gif

# Project specific
hash_md5_record.txt
!log_file.txt