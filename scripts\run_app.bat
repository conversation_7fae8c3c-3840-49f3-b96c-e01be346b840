@echo off
echo Starting Astro Automation...
echo.

REM Set the path to the project root directory (one level up from scripts)
set "PROJECT_ROOT=%~dp0.."
cd "%PROJECT_ROOT%"

REM Check if virtual environment exists
if not exist "venv\Scripts\activate.bat" (
    echo Virtual environment not found. Creating a new one...
    python -m venv venv
    
    if %ERRORLEVEL% neq 0 (
        echo Failed to create virtual environment. Please make sure Python is installed.
        pause
        exit /b 1
    )
    
    echo Installing required packages...
    call venv\Scripts\activate.bat
    pip install -r requirements.txt
    
    if %ERRORLEVEL% neq 0 (
        echo Failed to install required packages.
        pause
        exit /b 1
    )
    
    echo Virtual environment setup complete.
) else (
    echo Using existing virtual environment.
)

REM Activate the virtual environment
call venv\Scripts\activate.bat

echo.
echo Running Astro Automation...
echo.

REM Run app.py with any command-line arguments passed to this batch file
python app.py %*

REM Pause to keep the window open
echo.
echo Application execution completed.
pause
