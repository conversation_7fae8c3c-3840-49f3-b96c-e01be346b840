"""
Base API module that provides common functionality for interacting with the API.
This module serves as the foundation for all API-related operations.
"""
import json
import time
import traceback
from typing import Dict, Any, List, Optional, Union

import requests

from src.utils.logger import logger
from src.logic import recommendation
from src.logic import condition_parser


class ApiError(Exception):
    """Base exception for API-related errors."""
    pass


class AuthenticationError(ApiError):
    """Error during API authentication."""
    pass


class DataFetchError(ApiError):
    """Error fetching data from API."""
    pass


class ApiBase:
    """
    Base class for API operations.
    Provides common functionality for interacting with the API.
    """

    def __init__(self, debug_mode: bool = False):
        """
        Initialize the API base class.

        Args:
            debug_mode: Whether to enable debug logging
        """
        self.debug_mode = debug_mode

    def get_bearer_token(self, bearer_url: str, client_id: str = None, client_secret: str = None) -> str:
        """
        Get a bearer token for API authentication.

        Args:
            bearer_url: URL to get the bearer token
            client_id: Client ID for authentication (optional)
            client_secret: Client secret for authentication (optional)

        Returns:
            Bearer token as a string

        Raises:
            AuthenticationError: If authentication fails
        """
        try:
            # Prepare the request data
            data = {
                "grant_type": "client_credentials"
            }

            # Set up headers
            headers = {"Content-Type": "application/x-www-form-urlencoded"}

            # Make the request with HTTP Basic Authentication
            if client_id and client_secret:
                # Use HTTP Basic Authentication
                auth = (client_id, client_secret)

                # Debug logging to help troubleshoot
                if self.debug_mode:
                    logger.debug(f"Attempting to get bearer token from {bearer_url}")
                    logger.debug(f"Using client_id: {client_id}")
                    # Don't log the full secret, just the first few characters
                    if client_secret:
                        masked_secret = client_secret[:4] + "****" if len(client_secret) > 4 else "****"
                        logger.debug(f"Using client_secret: {masked_secret}")

                # Try with different authentication methods if the first one fails
                try:
                    # Method 1: Basic Auth in header
                    response = requests.post(bearer_url, data=data, headers=headers, auth=auth, timeout=30)
                    response.raise_for_status()
                except requests.exceptions.HTTPError as e:
                    if e.response.status_code == 401:
                        # Method 2: Include credentials in the data payload
                        data["client_id"] = client_id
                        data["client_secret"] = client_secret
                        response = requests.post(bearer_url, data=data, headers=headers, timeout=30)
                        response.raise_for_status()
            else:
                # Fall back to the old method if no credentials provided
                logger.warning("No client_id or client_secret provided for API authentication")
                response = requests.post(bearer_url, data=data, headers=headers, timeout=30)
                response.raise_for_status()

            # Parse the response
            token_data = response.json()
            token = token_data.get("access_token")

            if not token:
                raise AuthenticationError("No access token in response")

            if self.debug_mode:
                logger.debug(f"Successfully obtained bearer token")

            return token

        except requests.exceptions.RequestException as e:
            error_msg = f"Failed to get bearer token: {str(e)}"
            logger.error(error_msg)

            # Add more detailed error information
            if hasattr(e, 'response') and e.response is not None:
                logger.error(f"Response status code: {e.response.status_code}")
                logger.error(f"Response headers: {e.response.headers}")
                try:
                    error_details = e.response.json()
                    logger.error(f"Response body: {error_details}")
                except:
                    logger.error(f"Response body: {e.response.text}")

            # Try to use mock data for testing if configured
            if hasattr(self, 'use_mock_data') and self.use_mock_data:
                logger.warning("Using mock token for testing")
                return "mock_token_for_testing"

            raise AuthenticationError(error_msg) from e

    def make_api_request(self, token: str, request_url: str) -> Dict[str, Any]:
        """
        Make an API request with the provided token and URL.

        Args:
            token: Bearer token for authentication
            request_url: URL to make the request to

        Returns:
            Dict containing the JSON response data

        Raises:
            DataFetchError: If the request fails
        """
        if not token:
            error_msg = "No valid token provided for API request"
            logger.error(error_msg)
            raise DataFetchError(error_msg)

        headers = {"Authorization": f"Bearer {token}"}

        try:
            if self.debug_mode:
                logger.debug(f"Making API request to {request_url}")

            # Add a timeout to prevent hanging indefinitely
            response = requests.get(request_url, headers=headers, timeout=30)
            response.raise_for_status()

            # Parse the response
            data = response.json()

            if self.debug_mode:
                logger.debug(f"API request successful")

            return data

        except requests.exceptions.RequestException as e:
            error_msg = f"API request failed: {str(e)}"
            logger.error(error_msg)
            raise DataFetchError(error_msg) from e

    def _generate_mock_data(self, severity_levels: List[str]) -> List[Dict[str, Any]]:
        """
        Generate mock alarm data for testing when the API is unavailable.

        Args:
            severity_levels: List of severity levels to include

        Returns:
            List of mock alarm data
        """
        # Import the mock data module
        from src.utils.mock_data import generate_mock_alarms, process_mock_alarms

        # Generate and process mock alarms in one step
        return process_mock_alarms(
            generate_mock_alarms(severity_levels=severity_levels),
            severity_levels,
            "open"
        )

    def api_filter(self, bearer_url: str, api_req_url: str,
                  severity_levels: Optional[List[str]] = None,
                  status_filter: str = "open",
                  client_id: str = None,
                  client_secret: str = None) -> List[Dict[str, Any]]:
        """
        Filter API data to extract relevant alarm information.

        Args:
            bearer_url: URL to get the bearer token
            api_req_url: URL to make the API request
            severity_levels: List of severity levels to include (e.g., ["medium", "high"])
                            If None, defaults to ["low", "medium", "high"]
            status_filter: Status to filter by (default: "open")
            client_id: Client ID for authentication (optional)
            client_secret: Client secret for authentication (optional)

        Returns:
            List of filtered events
        """
        # Default to medium and high severity levels if not specified
        if severity_levels is None:
            severity_levels = ["medium", "high"]

        # Convert to lowercase for case-insensitive comparison
        severity_levels = [level.lower() for level in severity_levels]
        status_filter = status_filter.lower()

        if self.debug_mode:
            logger.debug(f"Filtering for severity levels: {severity_levels}, status: {status_filter}")

        try:
            # Get the bearer token
            token = self.get_bearer_token(bearer_url, client_id, client_secret)

            # Send the get request to API URL
            api_req_data = self.make_api_request(token, api_req_url)
            api_req_alarms = api_req_data.get("_embedded", {}).get("alarms", [])

            if self.debug_mode:
                logger.debug(f"Total alarms from API: {len(api_req_alarms)}")

            # Process and filter the alarms
            return self._process_alarms(api_req_alarms, severity_levels, status_filter)

        except Exception as e:
            logger.error(f"Error in api_filter: {str(e)}")
            logger.error(traceback.format_exc())

            # Check if we should use mock data
            if hasattr(self, 'use_mock_data') and self.use_mock_data:
                logger.warning("API call failed, using mock data instead")
                return self._generate_mock_data(severity_levels)

            return []

    def _process_alarms(self, alarms: List[Dict[str, Any]],
                       severity_levels: List[str],
                       status_filter: str) -> List[Dict[str, Any]]:
        """
        Process and filter alarms based on severity and status.

        Args:
            alarms: List of alarms from the API
            severity_levels: List of severity levels to include
            status_filter: Status to filter by

        Returns:
            List of filtered events
        """
        event_list = []
        recommendation_comment = recommendation.recommendation_mapping_logic(1)

        for alarm in alarms:
            # Extract relevant fields
            event_id = alarm.get("uuid", "")
            severity = alarm.get("priority_label", "").lower()
            status = alarm.get("status", "").lower()

            # Get other fields with proper fallbacks
            rule_strategy = alarm.get("rule_strategy", "Unknown Strategy")
            rule_method = alarm.get("rule_method", "Unknown Method")
            time_received = self._format_time(alarm.get("timestamp_occured_first", 0))
            alarms_count = alarm.get("events_count", 0)
            source_username = alarm.get("source_username", "Unknown")
            source_hostname = alarm.get("source_hostname", "Unknown")
            alarm_names = alarm.get("alarm_names", [])
            destination_name = alarm.get("destination_name", "Unknown")
            destination_username = alarm.get("destination_username", "Unknown")

            # Filter alarms based on severity and status
            if severity in severity_levels and status == status_filter:
                if self.debug_mode:
                    logger.debug(f"Alarm matched filters - ID: {event_id}, Severity: {severity}, Status: {status}")

                # Create a custom subject string using the extracted data
                # Store it in the filtered event for use in email subject
                filtered_event_subject = f"ASTRO | {severity.capitalize()} | {rule_strategy} | {time_received} | Alarm ID {event_id[-12:]}"

                # Create the filtered event
                filtered_event = {
                    "Ticket Event Classification": f"-{rule_strategy} \n-{rule_method}",
                    "Date & Time": f"{time_received}",
                    "Ticket Descriptions": f"{rule_method}",
                    "Ticket Assignment/Escalation": "Dear Techlab Automation Team, \n\n"
                                                   f"We detected {rule_method} from hostname : {source_hostname} "
                                                   "\n\nPlease Check Before take any Action, Thankyou Automation Team",
                    "Severity": f"{severity}",
                    "Event / Flow count": f"{alarms_count} events",
                    "Username": f"{source_username}",
                    "Source IP(s) & Hostname": f"{alarm_names} ({source_hostname})",
                    "Target IP(s) & Ports": f"{destination_name}   ({destination_username})",
                    "Recommendation": f"{recommendation_comment}",
                    "Event ID": f"{event_id}",
                    "Email Subject": filtered_event_subject  # Add the subject to the event for use in emails
                }

                event_list.append(filtered_event)
            elif self.debug_mode:
                logger.debug(f"Alarm filtered out - ID: {event_id}, Severity: {severity}, Status: {status}")

        return event_list

    def _format_time(self, timestamp: Union[int, str]) -> str:
        """
        Format a timestamp into a human-readable string.

        Args:
            timestamp: Timestamp in milliseconds or a string

        Returns:
            Formatted time string
        """
        try:
            # If timestamp is missing or 0, use current time instead of epoch
            if timestamp is None or timestamp == 0:
                # Only log this in debug mode to avoid flooding the logs
                if self.debug_mode:
                    logger.debug("Missing timestamp, using current time instead")
                timestamp = int(time.time() * 1000)  # Current time in milliseconds

            if isinstance(timestamp, str):
                # Try to convert string to int
                try:
                    timestamp = int(timestamp)
                except ValueError:
                    # If it's not a numeric string, it might be a date string
                    # Just return it as is
                    return timestamp

            # Convert from milliseconds to seconds
            timestamp_seconds = timestamp / 1000

            # Sanity check - if timestamp is too old (before 2020) or in the future, use current time
            current_time = time.time()
            if timestamp_seconds < 1577836800:  # Jan 1, 2020
                if self.debug_mode:
                    logger.debug(f"Timestamp too old ({timestamp_seconds}), using current time")
                timestamp_seconds = current_time
            elif timestamp_seconds > current_time + 86400:  # More than a day in the future
                if self.debug_mode:
                    logger.debug(f"Timestamp in the future ({timestamp_seconds}), using current time")
                timestamp_seconds = current_time

            # Format the time
            time_obj = time.localtime(timestamp_seconds)
            return time.strftime("%a, %b %d %Y, %I:%M:%S %p", time_obj)

        except (ValueError, TypeError) as e:
            if self.debug_mode:
                logger.debug(f"Error formatting timestamp {timestamp}: {str(e)}")
            # Use current time instead of "Unknown Time"
            time_obj = time.localtime(time.time())
            return time.strftime("%a, %b %d %Y, %I:%M:%S %p", time_obj)

    def count_severity(self, severity_levels: Optional[List[str]] = None,
                      status_filter: str = "open",
                      client_id: str = None,
                      client_secret: str = None) -> List[int]:
        """
        Count the number of alarms for each severity level.

        Args:
            severity_levels: List of severity levels to count
            status_filter: Status to filter by (default: "open")
            client_id: Client ID for authentication (optional)
            client_secret: Client secret for authentication (optional)

        Returns:
            List of counts for each severity level
        """
        try:
            # Default to medium and high severity levels if not specified
            if severity_levels is None:
                severity_levels = ["medium", "high"]

            # Convert to lowercase for case-insensitive comparison
            severity_levels = [level.lower() for level in severity_levels]
            status_filter = status_filter.lower()

            if self.debug_mode:
                logger.debug(f"Counting alarms for severity levels: {severity_levels}, status: {status_filter}")

            # Get the bearer token
            token = self.get_bearer_token("https://astro.alienvault.cloud/api/2.0/oauth/token", client_id, client_secret)

            # Send the get request to API URL
            api_req_data = self.make_api_request(token, "https://astro.alienvault.cloud/api/2.0/alarms/")
            api_req_alarms = api_req_data.get("_embedded", {}).get("alarms", [])

            # Initialize counts for each severity level
            severity_counts = {level: 0 for level in severity_levels}

            # Count alarms by severity
            for alarm in api_req_alarms:
                severity = alarm.get("priority_label", "").lower()
                status = alarm.get("status", "").lower()

                if severity in severity_levels and status == status_filter:
                    severity_counts[severity] += 1

            # Return counts as a list in the same order as severity_levels
            result = [severity_counts[level] for level in severity_levels]

            if self.debug_mode:
                logger.debug(f"Alarm counts: {result}")

            return result

        except Exception as e:
            logger.error(f"Error in count_severity: {str(e)}")
            logger.error(traceback.format_exc())

            # Check if we should use mock data
            if hasattr(self, 'use_mock_data') and self.use_mock_data:
                logger.warning("API call failed, using mock data for severity counts")

                # Use the mock data module
                from src.utils.mock_data import generate_mock_severity_counts

                # Generate mock counts
                mock_counts = generate_mock_severity_counts(severity_levels)

                if self.debug_mode:
                    logger.debug(f"Mock alarm counts: {mock_counts}")

                return mock_counts

            return [0] * len(severity_levels)

    def _get_mock_alarms(self) -> List[Dict[str, Any]]:
        """
        Get mock alarms for testing when the API is unavailable.
        Uses the centralized mock data generation function.

        Returns:
            List of mock alarms
        """
        # Import the mock data module
        from src.utils.mock_data import generate_mock_alarms

        # Generate mock alarms with only medium and high severity levels (per user preference)
        return generate_mock_alarms(
            severity_levels=["medium", "high"],
            count_per_severity=1,
            extra_medium_high=True,
            domain_suffix="example.com"
        )

    def trigger_email_condition(self, condition: str, bearer_url: str, client_id: str = None, client_secret: str = None) -> bool:
        """
        Check if any alarms meet the condition specified in the configuration.
        Uses a safe condition parser instead of eval() for security.

        Args:
            condition: String condition to evaluate for each alarm
            bearer_url: URL to get the bearer token
            client_id: Client ID for authentication (optional)
            client_secret: Client secret for authentication (optional)

        Returns:
            bool: True if any alarm meets the condition, False otherwise
        """
        try:
            # Get the bearer token
            token = self.get_bearer_token(bearer_url, client_id, client_secret)

            # Send the get request to API URL
            api_req_data = self.make_api_request(token, "https://astro.alienvault.cloud/api/2.0/alarms/")
            api_req_alarms = api_req_data.get("_embedded", {}).get("alarms", [])

            if self.debug_mode:
                logger.debug(f"Checking {len(api_req_alarms)} alarms with condition: {condition}")

            # Track which alarms match the condition
            matching_alarms = []

            # Use the safe condition parser instead of eval()
            for alarm in api_req_alarms:
                # Get key information for debugging
                alarm_id = alarm.get('uuid', 'unknown')[-12:]
                status = alarm.get('status', '').lower()
                severity = alarm.get('priority_label', '').lower()

                # Evaluate the condition
                result = condition_parser.safe_evaluate_condition(condition, alarm)

                if result:
                    matching_alarms.append({
                        'id': alarm_id,
                        'status': status,
                        'severity': severity
                    })
                elif self.debug_mode:
                    logger.debug(f"Alarm {alarm_id} (Status: {status}, Severity: {severity}) did not match condition")

            trigger = len(matching_alarms) > 0

            if trigger:
                if self.debug_mode:
                    logger.debug(f"Found {len(matching_alarms)} matching alarms:")
                    for alarm in matching_alarms:
                        logger.debug(f"  - ID: {alarm['id']}, Status: {alarm['status']}, Severity: {alarm['severity']}")
                logger.info("1st check: New Alarms detected")
            else:
                if self.debug_mode:
                    logger.debug("No alarms matched the condition")
                logger.info("1st check: No new Alarms")

            return trigger

        except Exception as e:
            logger.error(f"Error in trigger_email_condition: {str(e)}")
            logger.error(traceback.format_exc())

            # Check if we should use mock data
            if hasattr(self, 'use_mock_data') and self.use_mock_data:
                logger.warning("API call failed, using mock data for condition testing")

                # Get mock alarms using the renamed method
                mock_alarms = self._get_mock_alarms()

                if self.debug_mode:
                    logger.debug(f"Checking {len(mock_alarms)} mock alarms with condition: {condition}")

                # Track which mock alarms match the condition
                matching_alarms = []

                # Use the safe condition parser instead of eval()
                for alarm in mock_alarms:
                    # Get key information for debugging
                    alarm_id = alarm.get('uuid', 'unknown')[-12:]
                    status = alarm.get('status', '').lower()
                    severity = alarm.get('priority_label', '').lower()

                    # Evaluate the condition
                    result = condition_parser.safe_evaluate_condition(condition, alarm)

                    if result:
                        matching_alarms.append({
                            'id': alarm_id,
                            'status': status,
                            'severity': severity
                        })

                trigger = len(matching_alarms) > 0

                if trigger:
                    logger.info("1st check: New Alarms detected (mock data)")
                else:
                    logger.info("1st check: No new Alarms (mock data)")

                # Return the actual result based on the condition evaluation
                return trigger

            return False

    def update_config_subject(self, new_subject: str, config_file_path: str = "configs/profiles/config_umwt.json") -> None:
        """
        Update the email subject in the configuration file.

        Args:
            new_subject: New subject string
            config_file_path: Path to the configuration file
        """
        try:
            # Load the existing config file
            with open(config_file_path, "r") as config_file:
                config = json.load(config_file)

            # Update the subject in the Email section
            config["Email"]["subject"] = new_subject

            # Save the updated config back to the file
            with open(config_file_path, "w") as config_file:
                json.dump(config, config_file, indent=4)

            if self.debug_mode:
                logger.debug(f"Updated email subject in config to: {new_subject}")

        except Exception as e:
            logger.error(f"Error updating config subject: {str(e)}")
            logger.error(traceback.format_exc())
