"""
Hash calculation and comparison utilities.
This module provides functions for calculating and comparing hashes of data.
"""
import os
import json
import hashlib
import binascii
from typing import Dict, Any
from src.utils.logger import logger

# Debug mode flag
DEBUG_MODE = os.environ.get("ASTRO_DEBUG_MODE", "").lower() in ("true", "1", "yes", "y")


def calculate_md5_hash(dictionary: Dict[str, Any]) -> str:
    """
    Calculate MD5 hash of a dictionary.

    Args:
        dictionary: Dictionary to hash

    Returns:
        MD5 hash as a hexadecimal string
    """
    json_str = json.dumps(dictionary, sort_keys=True)
    md5_hash = hashlib.md5(json_str.encode('utf-8'))
    return md5_hash.hexdigest()


def calculate_crc32_hash(dictionary: Dict[str, Any]) -> int:
    """
    Calculate CRC32 hash of a dictionary.

    Args:
        dictionary: Dictionary to hash

    Returns:
        CRC32 hash as an integer
    """
    json_str = json.dumps(dictionary, sort_keys=True)
    data = json_str.encode('utf-8')
    crc_value = binascii.crc32(data)
    return crc_value


def compare_hash(file_path: str, current_hash: str) -> bool:
    """
    Compare current hash with the last hash in the file and update the hash file.

    This function has been simplified to always return True since we want to process
    alarms regardless of whether the hash has changed. However, it still logs whether
    the hash has changed and updates the hash file for tracking purposes.

    Args:
        file_path: Path to the hash file
        current_hash: Current hash to compare

    Returns:
        Always True to ensure processing continues
    """
    logger.debug(f"Comparing hash at {file_path}")

    try:
        # Check if file exists
        if not os.path.exists(file_path):
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

            # Create file and add hash
            with open(file_path, 'w') as file:
                file.write(f"{current_hash}\n")

            logger.info("Hash file created and current hash recorded")
        else:
            # File exists, read and compare
            with open(file_path, 'r+') as file:
                lines = file.readlines()
                if lines:
                    previous_hash = lines[-1].strip()

                    # Check if hash has changed
                    if previous_hash == current_hash:
                        logger.info("No changes detected in data (hash matches previous value)")
                    else:
                        # Add the new hash to the file
                        file.write(f"{current_hash}\n")
                        logger.info("Changes detected in data, new hash recorded")
                else:
                    # File exists but is empty
                    file.write(f"{current_hash}\n")
                    logger.info("Empty hash file, hash recorded")

    except Exception as e:
        logger.error(f"Error handling hash file: {str(e)}")
        if DEBUG_MODE:
            import traceback
            logger.debug(f"Traceback: {traceback.format_exc()}")

    # Always return True to ensure processing continues
    return True

