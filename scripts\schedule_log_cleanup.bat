@echo off
echo Astro Automation - Schedule Log Cleanup Task
echo ==========================================
echo.
echo This script will create a scheduled task to run log cleanup daily.
echo.

REM Get the current directory
set "CURRENT_DIR=%~dp0"
set "PROJECT_ROOT=%CURRENT_DIR%.."
set "PYTHON_CMD=python -m src.utils.cleanup_logs --force"

REM Check if Python is available
where python >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Python not found. Please make sure Python is installed and in your PATH.
    pause
    exit /b 1
)

echo Creating scheduled task to run log cleanup daily at 3:00 AM...
echo.

REM Create the scheduled task with working directory set to project root
schtasks /create /tn "Astro Automation Log Cleanup" /tr "%PYTHON_CMD%" /sc daily /st 03:00 /ru SYSTEM /f

if %ERRORLEVEL% equ 0 (
    echo Scheduled task created successfully.
    echo Log cleanup will run daily at 3:00 AM.
) else (
    echo Failed to create scheduled task. You may need to run this script as administrator.
)

echo.
pause
