# Standard library imports
import os
from pathlib import Path

def load_env_file(env_file_path=".env"):
    """
    Load environment variables from a .env file.

    Args:
        env_file_path: Path to the .env file

    Returns:
        bool: True if environment variables were loaded successfully, False otherwise
    """
    try:
        env_path = Path(env_file_path)
        if env_path.exists():
            # Count variables loaded
            variables_loaded = 0

            with open(env_path, 'r') as f:
                for line in f:
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue

                    try:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip()

                        # Skip empty keys or values
                        if not key or not value:
                            continue

                        os.environ[key] = value
                        variables_loaded += 1
                    except ValueError:
                        print(f"Warning: Invalid line in .env file: {line}")
                        continue

            if variables_loaded > 0:
                print(f"Loaded {variables_loaded} environment variables from {env_file_path}")
                return True
            else:
                print(f"Warning: No valid environment variables found in {env_file_path}")
                print("Using default values from configuration files.")
                return False
        else:
            print(f"Warning: .env file not found at {env_file_path}")
            print("Using default values from configuration files.")
            print("Run setup_credentials.py to create a .env file with your credentials.")
            return False
    except Exception as e:
        print(f"Error loading .env file: {e}")
        print("Using default values from configuration files.")
        return False
