"""
Centralized logging module for the application.
Provides a consistent interface for logging across the application with date-based organization.
"""
import logging
from logging.handlers import RotatingFileHandler
from datetime import datetime
import pathlib


class Logger:
    """
    Centralized logger class that provides a consistent interface for logging.
    Uses date-based directory structure for better log organization.
    """

    # Singleton instance
    _instance = None

    # Default log levels
    DEBUG = logging.DEBUG
    INFO = logging.INFO
    WARNING = logging.WARNING
    ERROR = logging.ERROR
    CRITICAL = logging.CRITICAL

    def __new__(cls, *_args, **_kwargs):
        """Ensure only one instance of Logger exists (Singleton pattern)"""
        if cls._instance is None:
            cls._instance = super(Logger, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self,
                 base_log_dir: str = "logs",
                 console_level: int = logging.INFO,
                 file_level: int = logging.DEBUG,
                 max_file_size: int = 10 * 1024 * 1024,  # 10 MB
                 backup_count: int = 5):
        """
        Initialize the logger with date-based directory structure.

        Args:
            base_log_dir: Base directory for logs
            console_level: Logging level for console output
            file_level: Logging level for file output
            max_file_size: Maximum size of log file before rotation (in bytes)
            backup_count: Number of backup log files to keep
        """
        # Only initialize once (singleton pattern)
        if self._initialized:
            return

        # Store configuration
        self.base_log_dir = base_log_dir
        self.console_level = console_level
        self.file_level = file_level
        self.max_file_size = max_file_size
        self.backup_count = backup_count

        # Create the logger
        self.logger = logging.getLogger('astro_automation')
        self.logger.setLevel(logging.DEBUG)  # Capture all levels

        # Clear any existing handlers
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)

        # Set up log directories and files
        self._setup_log_directories()

        # Create handlers
        self._setup_handlers()

        self._initialized = True

    def _setup_log_directories(self):
        """Set up date-based log directories."""
        # Get current date
        now = datetime.now()
        date_str = now.strftime("%Y-%m-%d")
        time_str = now.strftime("%H%M%S")

        # Create base log directory
        base_dir = pathlib.Path(self.base_log_dir)
        base_dir.mkdir(exist_ok=True)

        # Create daily directory
        daily_dir = base_dir / "daily"
        daily_dir.mkdir(exist_ok=True)

        # Create date-specific directory
        self.date_dir = daily_dir / date_str
        self.date_dir.mkdir(exist_ok=True)

        # Set up log file paths
        self.app_log_file = str(self.date_dir / f"application_{time_str}.log")
        self.error_log_file = str(self.date_dir / f"error_{time_str}.log")

        # Also maintain a current log file at the root level
        self.current_app_log = str(base_dir / "application.log")
        self.current_error_log = str(base_dir / "error.log")

    def _setup_handlers(self):
        """Set up the console and file handlers with date-based organization."""
        # Create console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(self.console_level)

        # Create application log handler with rotation
        app_handler = RotatingFileHandler(
            self.app_log_file,
            maxBytes=self.max_file_size,
            backupCount=self.backup_count
        )
        app_handler.setLevel(self.file_level)

        # Create current application log handler
        current_app_handler = RotatingFileHandler(
            self.current_app_log,
            maxBytes=self.max_file_size,
            backupCount=1
        )
        current_app_handler.setLevel(self.file_level)

        # Create error log handler that only captures errors and above
        error_handler = RotatingFileHandler(
            self.error_log_file,
            maxBytes=self.max_file_size,
            backupCount=self.backup_count
        )
        error_handler.setLevel(logging.ERROR)

        # Create current error log handler
        current_error_handler = RotatingFileHandler(
            self.current_error_log,
            maxBytes=self.max_file_size,
            backupCount=1
        )
        current_error_handler.setLevel(logging.ERROR)

        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

        # Add formatter to handlers
        console_handler.setFormatter(formatter)
        app_handler.setFormatter(formatter)
        current_app_handler.setFormatter(formatter)
        error_handler.setFormatter(formatter)
        current_error_handler.setFormatter(formatter)

        # Add handlers to logger
        self.logger.addHandler(console_handler)
        self.logger.addHandler(app_handler)
        self.logger.addHandler(current_app_handler)
        self.logger.addHandler(error_handler)
        self.logger.addHandler(current_error_handler)

    def debug(self, message: str, *args, **kwargs):
        """Log a debug message."""
        self.logger.debug(message, *args, **kwargs)

    def info(self, message: str, *args, **kwargs):
        """Log an info message."""
        self.logger.info(message, *args, **kwargs)

    def warning(self, message: str, *args, **kwargs):
        """Log a warning message."""
        self.logger.warning(message, *args, **kwargs)

    def error(self, message: str, *args, **kwargs):
        """Log an error message."""
        self.logger.error(message, *args, **kwargs)

    def critical(self, message: str, *args, **kwargs):
        """Log a critical message."""
        self.logger.critical(message, *args, **kwargs)

    def exception(self, message: str, *args, **kwargs):
        """Log an exception message with traceback."""
        self.logger.exception(message, *args, **kwargs)

    def set_level(self, level: int):
        """Set the logging level for all handlers."""
        self.logger.setLevel(level)
        for handler in self.logger.handlers:
            handler.setLevel(level)

    def get_log_file_path(self):
        """Get the current application log file path."""
        return self.app_log_file

    def get_error_log_file_path(self):
        """Get the current error log file path."""
        return self.error_log_file


# Create a global logger instance
logger = Logger()

# Convenience functions
debug = logger.debug
info = logger.info
warning = logger.warning
error = logger.error
critical = logger.critical
exception = logger.exception
get_log_file_path = logger.get_log_file_path
get_error_log_file_path = logger.get_error_log_file_path
