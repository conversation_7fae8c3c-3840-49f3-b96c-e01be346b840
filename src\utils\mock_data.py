"""
Mock data generation utility for testing and development.
This module provides functions to generate mock alarm data when the API is unavailable.
"""
import time
import uuid
import random
from typing import List, Dict, Any, Optional

from src.utils.logger import logger
from src.utils.config import get_config


# Common alarm types for testing
DEFAULT_ALARM_TYPES = [
    ("Brute Force Authentication", "Failed Login Attempts"),
    ("Anomalous User Behavior", "Successful Logon to Default Account"),
    ("Malware Detection", "Suspicious File Detected"),
    ("Network Scan", "Port Scanning Activity"),
    ("Data Exfiltration", "Unusual Data Transfer")
]


def generate_mock_alarms(
    severity_levels: Optional[List[str]] = None,
    count_per_severity: int = 1,
    extra_medium_high: bool = True,
    domain_suffix: str = "none"
) -> List[Dict[str, Any]]:
    """
    Generate mock alarms for testing when the API is unavailable.

    Args:
        severity_levels: List of severity levels to include (defaults to ["low", "medium", "high"])
        count_per_severity: Number of alarms to generate per severity level
        extra_medium_high: Whether to add extra medium and high severity alarms
        domain_suffix: Domain suffix to use for hostnames (e.g., "example.com" or "none")

    Returns:
        List of mock alarms
    """
    # Default to medium and high severity levels if not specified (per user preference)
    if severity_levels is None:
        severity_levels = ["medium", "high"]

    # Convert to lowercase for consistency
    severity_levels = [level.lower() for level in severity_levels]

    # Try to get alarm types from configuration
    try:
        api_config = get_config("api")
        alarm_types = api_config.get("Mock", {}).get("alarm_types", DEFAULT_ALARM_TYPES)
    except Exception:
        alarm_types = DEFAULT_ALARM_TYPES

    logger.info(f"Generating mock alarms for severity levels: {severity_levels}")
    logger.debug(f"Using domain suffix: {domain_suffix}")

    # Generate mock alarms
    mock_alarms = []

    # Generate alarms for each severity level
    for severity in severity_levels:
        for _ in range(count_per_severity):
            # Create a mock alarm
            mock_alarm = _create_mock_alarm(severity, alarm_types, domain_suffix)
            mock_alarms.append(mock_alarm)

    # Add extra medium and high severity alarms if requested
    if extra_medium_high:
        extra_severities = [s for s in ["medium", "high"] if s in severity_levels]
        for severity in extra_severities:
            for _ in range(2):  # Add 2 more alarms of each severity
                mock_alarm = _create_mock_alarm(severity, alarm_types, domain_suffix)
                mock_alarms.append(mock_alarm)

    logger.info(f"Generated {len(mock_alarms)} mock alarms")
    return mock_alarms


def _create_mock_alarm(
    severity: str,
    alarm_types: List[tuple],
    domain_suffix: str
) -> Dict[str, Any]:
    """
    Create a single mock alarm.

    Args:
        severity: Severity level for the alarm
        alarm_types: List of alarm type tuples (strategy, method)
        domain_suffix: Domain suffix to use for hostnames

    Returns:
        Mock alarm dictionary
    """
    # Pick a random alarm type
    alarm_type = random.choice(alarm_types)

    # Generate a random UUID
    event_id = str(uuid.uuid4())

    # Current timestamp
    current_time = int(time.time() * 1000)

    # Create a mock alarm
    return {
        "uuid": event_id,
        "priority_label": severity,
        "status": "open",
        "rule_strategy": alarm_type[0],
        "rule_method": alarm_type[1],
        "timestamp_occured_first": current_time,
        "events_count": random.randint(1, 10),
        "source_username": f"user{random.randint(1, 100)}",
        "source_hostname": f"host{random.randint(1, 100)}.{domain_suffix}",
        "alarm_names": [f"alarm{random.randint(1, 5)}"],
        "destination_name": f"server{random.randint(1, 10)}.{domain_suffix}",
        "destination_username": f"admin{random.randint(1, 5)}"
    }


def generate_mock_severity_counts(severity_levels: Optional[List[str]] = None) -> List[int]:
    """
    Generate mock severity counts for testing.

    Args:
        severity_levels: List of severity levels to include (defaults to ["low", "medium", "high"])

    Returns:
        List of counts for each severity level
    """
    # Default to medium and high severity levels if not specified (per user preference)
    if severity_levels is None:
        severity_levels = ["medium", "high"]

    # Generate random counts
    mock_counts = [random.randint(1, 5) for _ in severity_levels]

    logger.info(f"Generated mock severity counts: {mock_counts}")
    return mock_counts


def process_mock_alarms(
    alarms: List[Dict[str, Any]],
    severity_levels: Optional[List[str]] = None,
    status_filter: str = "open"
) -> List[Dict[str, Any]]:
    """
    Process mock alarms to match the format expected by the application.

    Args:
        alarms: List of mock alarms
        severity_levels: List of severity levels to include (defaults to ["medium", "high"])
        status_filter: Status to filter by (default: "open")

    Returns:
        List of processed alarms
    """
    # Default to medium and high severity levels if not specified (per user preference)
    if severity_levels is None:
        severity_levels = ["medium", "high"]

    # Convert to lowercase for case-insensitive comparison
    severity_levels = [level.lower() for level in severity_levels]
    status_filter = status_filter.lower()

    # Import here to avoid circular imports
    from src.logic import recommendation

    # Get a recommendation
    recommendation_comment = recommendation.recommendation_mapping_logic()

    # Process alarms
    processed_alarms = []
    for alarm in alarms:
        # Extract relevant fields
        event_id = alarm.get("uuid", "")
        severity = alarm.get("priority_label", "").lower()
        status = alarm.get("status", "").lower()

        # Filter alarms based on severity and status
        if severity in severity_levels and status == status_filter:
            # Format timestamp
            timestamp = alarm.get("timestamp_occured_first", 0)
            if timestamp:
                timestamp_seconds = timestamp / 1000
                time_obj = time.localtime(timestamp_seconds)
                time_received = time.strftime("%a, %b %d %Y, %I:%M:%S %p", time_obj)
            else:
                time_obj = time.localtime(time.time())
                time_received = time.strftime("%a, %b %d %Y, %I:%M:%S %p", time_obj)

            # Get other fields with proper fallbacks
            rule_strategy = alarm.get("rule_strategy", "Unknown Strategy")
            rule_method = alarm.get("rule_method", "Unknown Method")
            alarms_count = alarm.get("events_count", 0)
            source_username = alarm.get("source_username", "Unknown")
            source_hostname = alarm.get("source_hostname", "Unknown")
            alarm_names = alarm.get("alarm_names", [])
            destination_name = alarm.get("destination_name", "Unknown")
            destination_username = alarm.get("destination_username", "Unknown")

            # Create a custom subject string
            filtered_event_subject = f"ASTRO | {severity.capitalize()} | {rule_strategy} | {time_received} | Alarm ID {event_id[-12:]}"

            # Create the filtered event
            filtered_event = {
                "Ticket Event Classification": f"-{rule_strategy} \n-{rule_method}",
                "Date & Time": f"{time_received}",
                "Ticket Descriptions": f"{rule_method}",
                "Ticket Assignment/Escalation": "Dear Techlab Automation Team, \n\n"
                                               f"We detected {rule_method} from hostname : {source_hostname} "
                                               "\n\nPlease Check Before take any Action, Thankyou Automation Team",
                "Severity": f"{severity}",
                "Event / Flow count": f"{alarms_count} events",
                "Username": f"{source_username}",
                "Source IP(s) & Hostname": f"{alarm_names} ({source_hostname})",
                "Target IP(s) & Ports": f"{destination_name}   ({destination_username})",
                "Recommendation": f"{recommendation_comment}",
                "Event ID": f"{event_id}",
                "Email Subject": filtered_event_subject
            }

            processed_alarms.append(filtered_event)

    logger.info(f"Processed {len(processed_alarms)} mock alarms")
    return processed_alarms
