"""
Setup script for Gmail OAuth2 authentication.

This script helps you set up OAuth2 authentication for Gmail.
"""
import os
import sys
import json
import argparse
from pathlib import Path

# Third-party imports
from google_auth_oauthlib.flow import InstalledAppFlow
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials

# Constants
GMAIL_TOKEN_FILE = "gmail_token.json"
GMAIL_CREDENTIALS_FILE = "gmail_credentials.json"
GMAIL_SCOPES = ["https://mail.google.com/"]


def setup_argument_parser():
    """Set up command line argument parser."""
    parser = argparse.ArgumentParser(description="Gmail OAuth2 Setup")
    parser.add_argument(
        "--email",
        type=str,
        help="Gmail email address to authenticate"
    )
    parser.add_argument(
        "--client-id",
        type=str,
        help="Google OAuth2 client ID"
    )
    parser.add_argument(
        "--client-secret",
        type=str,
        help="Google OAuth2 client secret"
    )
    return parser


def create_credentials_file(client_id, client_secret):
    """
    Create the Gmail credentials file.

    Args:
        client_id: Google OAuth2 client ID
        client_secret: Google OAuth2 client secret

    Returns:
        True if the credentials file was created successfully, False otherwise
    """
    try:
        # Create the credentials data
        credentials_data = {
            "installed": {
                "client_id": client_id,
                "client_secret": client_secret,
                "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                "token_uri": "https://oauth2.googleapis.com/token",
                "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
                "redirect_uris": ["http://localhost"]
            }
        }

        # Save the credentials data to the credentials file
        with open(GMAIL_CREDENTIALS_FILE, "w") as credentials_file:
            json.dump(credentials_data, credentials_file)
        print(f"Created Gmail credentials file: {GMAIL_CREDENTIALS_FILE}")
        return True
    except Exception as e:
        print(f"Error creating credentials file: {str(e)}")
        return False


def get_oauth2_credentials(email):
    """
    Get OAuth2 credentials for Gmail.

    Args:
        email: Gmail email address to authenticate

    Returns:
        Credentials object, or None if authentication fails
    """
    try:
        # Check if we have a credentials file
        credentials_path = Path(GMAIL_CREDENTIALS_FILE)
        if not credentials_path.exists():
            print(f"Gmail credentials file not found: {GMAIL_CREDENTIALS_FILE}")
            print("Please provide --client-id and --client-secret to create the credentials file")
            return None

        # Create the flow
        flow = InstalledAppFlow.from_client_secrets_file(
            GMAIL_CREDENTIALS_FILE, GMAIL_SCOPES
        )

        # Run the flow
        credentials = flow.run_local_server(port=0)
        print("Successfully obtained Gmail OAuth2 credentials")
        return credentials
    except Exception as e:
        print(f"Error getting OAuth2 credentials: {str(e)}")
        return None


def save_credentials(credentials, email):
    """
    Save credentials to the token file.

    Args:
        credentials: Credentials object to save
        email: Gmail email address associated with the credentials

    Returns:
        True if the credentials were saved successfully, False otherwise
    """
    try:
        # Convert credentials to a dictionary
        token_data = json.loads(credentials.to_json())
        # Add the email to the token data
        token_data["email"] = email

        # Save the token data to the token file
        with open(GMAIL_TOKEN_FILE, "w") as token_file:
            json.dump(token_data, token_file)
        print(f"Saved Gmail OAuth2 credentials to {GMAIL_TOKEN_FILE}")
        return True
    except Exception as e:
        print(f"Error saving credentials: {str(e)}")
        return False


def main():
    """Main function for the setup script."""
    # Parse command line arguments
    parser = setup_argument_parser()
    args = parser.parse_args()

    # Check if we need to create the credentials file
    if args.client_id and args.client_secret:
        if not create_credentials_file(args.client_id, args.client_secret):
            print("Failed to create credentials file")
            return

    # Check if we have an email address
    if not args.email:
        print("Please provide a Gmail email address with --email")
        return

    # Get OAuth2 credentials
    credentials = get_oauth2_credentials(args.email)
    if not credentials:
        print("Failed to get OAuth2 credentials")
        return

    # Save the credentials
    if not save_credentials(credentials, args.email):
        print("Failed to save credentials")
        return

    print("\nGmail OAuth2 setup completed successfully!")
    print(f"Email: {args.email}")
    print(f"Token file: {GMAIL_TOKEN_FILE}")
    print("\nYou can now use OAuth2 authentication for Gmail SMTP.")


if __name__ == "__main__":
    main()
