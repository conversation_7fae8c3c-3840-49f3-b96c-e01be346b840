{"Logging": {"base_log_dir": "logs", "daily_log_dir": "daily", "archive_log_dir": "archive", "log_format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "date_format": "%Y-%m-%d %H:%M:%S", "file_log_level": "INFO", "console_log_level": "INFO", "max_log_size_mb": 10, "backup_count": 5, "file_handler": true, "console_handler": true, "filename_format": "app_{date}.log"}, "Debug": {"debug_mode": false, "log_level": "DEBUG", "verbose": false, "log_api_responses": false}, "Cleanup": {"days_to_keep": 7, "max_archive_days": 30, "auto_cleanup": true}}