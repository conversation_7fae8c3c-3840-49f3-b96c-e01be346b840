"""
Configuration utility for Astro Automation.

This module provides a centralized way to load and access configuration files.
It supports loading JSON configuration files from the config directory and
accessing nested configuration values using dot notation.
"""
import os
import json
from pathlib import Path
import re
from typing import Any, Dict, Optional, Union

# Project-specific imports
from src.utils.logger import logger


class Config:
    """Configuration manager for Astro Automation."""

    def __init__(self, config_dir: Optional[str] = None):
        """
        Initialize the configuration manager.

        Args:
            config_dir: Optional path to the configuration directory.
                        If not provided, defaults to the 'config' directory
                        relative to the project root.
        """
        # Use the provided config_dir or default to the config directory
        if config_dir:
            self.config_dir = Path(config_dir)
        else:
            # Find the project root (where the config directory is located)
            current_dir = Path(__file__).parent.parent.parent
            self.config_dir = current_dir / "config"

        logger.debug(f"Using configuration directory: {self.config_dir}")
        self.configs = {}

    def load(self, config_name: str) -> Dict[str, Any]:
        """
        Load a configuration file by name.

        Args:
            config_name: Name of the configuration file (without extension)
                         or path relative to the config directory.

        Returns:
            Dict containing the configuration data.

        Raises:
            FileNotFoundError: If the configuration file doesn't exist.
        """
        # Return cached config if available
        if config_name in self.configs:
            return self.configs[config_name]

        # Try to find the configuration file
        config_path = self.config_dir / f"{config_name}.json"

        # If the path doesn't exist, check if it's in a subdirectory
        if not config_path.exists():
            # Check if config_name already includes a path
            if "/" in config_name or "\\" in config_name:
                config_path = self.config_dir / f"{config_name}.json"
            else:
                # Check subdirectories
                for subdir in self.config_dir.iterdir():
                    if subdir.is_dir():
                        potential_path = subdir / f"{config_name}.json"
                        if potential_path.exists():
                            config_path = potential_path
                            break

        # If the file still doesn't exist, raise an error
        if not config_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {config_path}")

        # Load the configuration file
        try:
            with open(config_path, 'r') as f:
                config_data = json.load(f)

            # Process environment variable substitutions
            config_data = self._substitute_env_vars(config_data)

            # Cache the configuration
            self.configs[config_name] = config_data
            logger.debug(f"Loaded configuration from {config_path}")

            return config_data

        except json.JSONDecodeError as e:
            logger.error(f"Error parsing configuration file {config_path}: {e}")
            raise

    def _substitute_env_vars(self, config_data: Any) -> Any:
        """
        Substitute environment variables in configuration values.

        Environment variables are specified as ${VAR_NAME} in the configuration.

        Args:
            config_data: Configuration data to process.

        Returns:
            Configuration data with environment variables substituted.
        """
        if isinstance(config_data, dict):
            return {k: self._substitute_env_vars(v) for k, v in config_data.items()}
        elif isinstance(config_data, list):
            return [self._substitute_env_vars(item) for item in config_data]
        elif isinstance(config_data, str):
            # Find all environment variables in the string
            env_vars = re.findall(r'\${([^}]+)}', config_data)

            # Substitute each environment variable
            result = config_data
            for var in env_vars:
                env_value = os.environ.get(var, '')
                if not env_value:
                    logger.warning(f"Environment variable {var} not found")
                result = result.replace(f"${{{var}}}", env_value)

            return result
        else:
            return config_data

    def get(self, config_name: str, key: Optional[str] = None, default: Any = None) -> Any:
        """
        Get a configuration value.

        Args:
            config_name: Name of the configuration file (without extension).
            key: Optional key to retrieve from the configuration.
                 Can use dot notation for nested keys (e.g., "Email.sender").
            default: Default value to return if the key doesn't exist.

        Returns:
            The configuration value, or the default if the key doesn't exist.
        """
        try:
            config = self.load(config_name)
        except FileNotFoundError:
            logger.warning(f"Configuration file {config_name} not found, returning default")
            return default

        if key is None:
            return config

        # Support nested keys with dot notation (e.g., "Email.sender")
        if "." in key:
            parts = key.split(".")
            value = config
            for part in parts:
                if not isinstance(value, dict) or part not in value:
                    return default
                value = value[part]
            return value

        return config.get(key, default)

    def get_profile(self, profile_name: str = "default") -> Dict[str, Any]:
        """
        Get a profile configuration.

        Args:
            profile_name: Name of the profile (without extension).

        Returns:
            Dict containing the profile configuration.
        """
        try:
            return self.load(f"profiles/{profile_name}")
        except FileNotFoundError:
            # Fall back to default profile if the requested one doesn't exist
            logger.warning(f"Profile {profile_name} not found, falling back to default")
            try:
                return self.load("profiles/default")
            except FileNotFoundError:
                logger.error("Default profile not found")
                return {}


# Create a singleton instance for global use
config_manager = Config()


def get_config(config_name: str, key: Optional[str] = None, default: Any = None) -> Any:
    """
    Get a configuration value using the global config manager.

    Args:
        config_name: Name of the configuration file (without extension).
        key: Optional key to retrieve from the configuration.
             Can use dot notation for nested keys (e.g., "Email.sender").
        default: Default value to return if the key doesn't exist.

    Returns:
        The configuration value, or the default if the key doesn't exist.
    """
    return config_manager.get(config_name, key, default)


def get_profile(profile_name: str = "default") -> Dict[str, Any]:
    """
    Get a profile configuration using the global config manager.

    Args:
        profile_name: Name of the profile (without extension).

    Returns:
        Dict containing the profile configuration.
    """
    return config_manager.get_profile(profile_name)
