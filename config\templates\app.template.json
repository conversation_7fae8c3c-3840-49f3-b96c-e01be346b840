{"Application": {"name": "Astro Automation", "version": "1.0.0", "debug_mode": false, "polling_interval": 180}, "Triggers": {"severity_levels": ["low", "medium", "high"], "trigger_email_condition": "Severity in ['medium', 'high']"}, "Paths": {"templates_dir": "resources/templates", "exports_dir": "resources/exports", "hash_dir": "data/hash"}, "Service": {"polling_interval": 180, "max_consecutive_errors": 5, "error_wait_time": 30}, "Debug": {"debug_mode": true, "log_level": "DEBUG"}}