# Project Structure Documentation

This document outlines the organization of the Astro Automation project, explaining the purpose of each directory and file.

## Directory Structure

```
Astro_Automation/
├── configs/                  # All configuration files
│   ├── app/                  # Application configuration
│   │   ├── app_config.json   # Main application settings
│   │   └── gdrive_config.json # Google Drive API settings
│   ├── email/                # Email configuration
│   │   └── email_config.json # Email settings and templates
│   ├── logging/              # Logging configuration
│   │   └── logging_config.json # Logging settings
│   └── profiles/             # Profile-specific configurations
│       └── config_umwt.json  # UMWT profile configuration
├── docs/                     # Documentation
│   └── images/               # Documentation images
├── export_method/            # Export functionality
│   ├── email_server.py       # Email sending functionality
│   ├── export_docx.py        # DOCX export
│   ├── export_html.py        # HTML export
│   └── export_spreadsheet.py # Excel/CSV export
├── logic_method/             # Business logic
│   ├── api_base.py           # Base API functionality
│   ├── api_method.py         # API methods
│   ├── hash.py               # Hash calculation
│   ├── profiler_controller.py # Profiler management
│   └── recommendation.py     # Gemini AI recommendations
├── logs/                     # All logs
│   ├── daily/                # Daily logs with date structure
│   │   └── YYYY-MM-DD/       # Logs for specific dates
│   ├── archive/              # Archived old logs
│   ├── application.log       # Current application log
│   └── error.log             # Current error log
├── profiler/                 # Profiler modules
│   ├── base_profiler.py      # Base profiler class
│   └── profiler_3.py         # UMWT profiler implementation
├── resources/                # All static resources
│   ├── exports/              # Generated exports
│   ├── hash/                 # Hash storage
│   ├── templates/            # Email templates
│   └── temp/                 # Temporary files
├── tests/                    # Test files
├── utilities/                # Utility functions
│   ├── config_loader.py      # Configuration loading
│   ├── credential_manager.py # Credential management
│   ├── env_loader.py         # Environment variable loading
│   ├── log_manager.py        # Log rotation and cleanup
│   ├── logger.py             # Logging functionality
│   └── utility_function.py   # General utility functions
├── .env                      # Environment variables
├── .gitignore                # Git ignore file
├── main.py                   # Main entry point
└── requirements.txt          # Dependencies
```

## Key Components

### Configuration System

The configuration system has been reorganized to use a structured approach:

1. **App Configuration**: Contains settings for API credentials, URLs, and feature toggles
2. **Email Configuration**: Contains email server settings, templates, and recipient lists
3. **Logging Configuration**: Contains logging levels, formats, and file paths

All configuration files use JSON format for consistency and support environment variable substitution using the `${VARIABLE_NAME}` syntax.

### Logging System

The logging system has been improved to use a date-based directory structure:

1. **Daily Logs**: Stored in `logs/daily/YYYY-MM-DD/` directories
2. **Archived Logs**: Old logs are archived in `logs/archive/YYYY-MM-DD/` directories
3. **Current Logs**: The most recent logs are also available at `logs/application.log` and `logs/error.log`
4. **Log Rotation**: Logs are automatically rotated when they reach a configurable size
5. **Log Management**: The `utilities/log_manager.py` script handles log archiving and cleanup

### Resource Management

Resources are now organized into specific directories:

1. **Templates**: Email and document templates in `resources/templates/`
2. **Hash**: Hash files for data integrity in `resources/hash/`
3. **Temp**: Temporary files that will be deleted after use in `resources/temp/`
4. **Exports**: Generated files for export in `resources/exports/`

## Usage Guidelines

### Configuration

Use the `ConfigLoader` class to access configuration settings:

```python
from src.utils.config_loader import get_app_config, get_email_config

# Get application configuration
app_config = get_app_config()
api_username = app_config["API_Credentials"]["api_username"]

# Get email configuration
email_config = get_email_config()
smtp_server = email_config["SMTP"]["server"]
```

### Logging

Use the centralized logger for all logging:

```python
from src.utils.logger import info, error, debug

# Log messages at different levels
info("Application started")
debug("Processing data...")
error("Failed to connect to API")
```

### Templates

Access templates from the resources directory:

```python
from pathlib import Path

template_path = Path("resources/templates/email_template.html")
with open(template_path, "r") as f:
    template_content = f.read()
```

## Best Practices

1. **Configuration**: Store all configuration in the appropriate config files, not in code
2. **Sensitive Data**: Use environment variables for sensitive information
3. **Logging**: Use the centralized logger instead of print statements
4. **Path Handling**: Use `pathlib.Path` for cross-platform path handling
5. **Resource Access**: Access resources through the resources directory structure

## Migration Notes

If you're working with existing code, note the following changes:

1. Configuration files have moved from `configs_file/` to `configs/profiles/`
2. Templates have moved from `jinja_template/` to `resources/templates/`
3. Logs are now stored in a date-based structure in `logs/daily/YYYY-MM-DD/`
4. Hash files have moved from `hash/` to `resources/hash/`
5. Documentation has moved from `documentation/` to `docs/`
6. Sensitive credentials have been moved to environment variables in `.env`

When updating existing code, make sure to update paths and imports accordingly.

## Log Management

The new log management system provides automatic archiving and cleanup of old logs:

```python
from src.utils.log_manager import manage_logs

# Archive logs older than 7 days and delete archived logs older than 30 days
result = manage_logs(days_to_keep=7, max_archive_days=30)
print(f"Archived {len(result['archived_directories'])} directories")
print(f"Deleted {result['deleted_directories']} directories")
```

You can also run the log manager directly:

```
python -m utilities.log_manager
```
