"""
Test script for the email sending functionality.
This script tests the actual sending of emails using the email_server module.
"""
import os
import sys
import json
import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from datetime import datetime

# Add the project root to the Python path to allow importing project modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment variables from .env file
from src.utils.env_loader import load_env_file
load_env_file()

# Import the necessary modules
from src.export.email_server import create_html_email_body
from src.utils.config_loader import get_email_config
from src.utils.logger import logger

# Create a mock profiler class for testing
class MockProfiler:
    def __init__(self):
        # Load email configuration
        self.config = get_email_config()

        # Set up basic profiler attributes
        self.document_title = "Test Email - Astro Automation"
        self.sender = os.environ.get("ASTRO_SMTP_EMAIL", "")
        self.receiver = [os.environ.get("ASTRO_TEST_EMAIL", self.sender)]  # Default to sender if test email not set
        self.smtp_password = os.environ.get("ASTRO_SMTP_PASSWORD", "")
        self.smtp_server = self.config["SMTP"]["server"]
        self.smtp_port = self.config["SMTP"]["port"]
        self.subject = "Test Email - Astro Automation"
        self.message = "This is a test email from Astro Automation."

        # Set up paths for attachments (not used in this test)
        self.directory = "output"
        self.attachment_file1 = os.path.join(self.directory, "test_attachment.xlsx")
        self.attachment_file2 = os.path.join(self.directory, "test_attachment.csv")
        self.attachment_file3 = os.path.join(self.directory, "test_attachment.docx")

        # Email sending flags
        self.send_email = True
        self.send_xlsx = False
        self.send_csv = False
        self.send_docx = False
        self.send_html = False

        # Other flags
        self.upload_gdrive = False
        self.delete_local = False

        # Data for email
        self.data = create_mock_data()

# Create some mock alarm data for testing
def create_mock_data():
    """Create mock alarm data for testing."""
    return [
        {
            "Ticket Event Classification": "Brute Force Authentication",
            "Date & Time": datetime.now().strftime("%a, %b %d %Y, %I:%M:%S %p"),
            "Ticket Descriptions": "Brute Force Authentication",
            "Ticket Assignment/Escalation": "Dear SOC Team, \n\nWe detected Brute Force Authentication from hostname : workstation1.example.com \n\nPlease Check Before take any Action, Thankyou SOC Team",
            "Severity": "medium",
            "Event / Flow count": "5 events",
            "Username": "user123",
            "Source IP(s) & Hostname": "************* (workstation1.example.com)",
            "Target IP(s) & Ports": "******** (server.example.com)",
            "Recommendation": "Investigate the source IP and check for multiple failed login attempts. Consider temporarily blocking the source IP if suspicious activity continues.",
            "Event ID": "TEST-001",
            "Email Subject": f"TEST | Medium | Brute Force Authentication | {datetime.now().strftime('%a, %b %d %Y, %I:%M:%S %p')} | Alarm ID TEST-001"
        }
    ]

def test_email_sending():
    """Test the email sending functionality."""
    print("Testing email sending functionality...")

    # Create a mock profiler
    profiler = MockProfiler()

    # Check if we have the required credentials
    if not profiler.sender or not profiler.smtp_password:
        print("ERROR: Missing email credentials. Please set ASTRO_SMTP_EMAIL and ASTRO_SMTP_PASSWORD environment variables.")
        return False

    print(f"Sending test email from {profiler.sender} to {profiler.receiver}")

    # Create a simple email message
    msg = MIMEMultipart()
    msg["From"] = profiler.sender
    msg["To"] = ", ".join(profiler.receiver)
    msg["Subject"] = profiler.subject

    # Add plain text body
    msg.attach(MIMEText(profiler.message, "plain"))

    # Add HTML body
    html_content = f"""
    <html>
    <body>
        <h1>Test Email from Astro Automation</h1>
        <p>This is a test email to verify the email sending functionality.</p>
        <p>Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </body>
    </html>
    """
    msg.attach(MIMEText(html_content, "html"))

    # Create SSL context
    context = ssl.create_default_context()

    try:
        # Connect to SMTP server
        print("Connecting to SMTP server...")
        server = smtplib.SMTP(profiler.smtp_server, profiler.smtp_port)
        server.starttls(context=context)

        # Login to SMTP server
        print("Logging into SMTP server...")
        server.login(profiler.sender, profiler.smtp_password)

        # Send email
        print("Sending email...")
        server.sendmail(profiler.sender, profiler.receiver, msg.as_string())

        print("Email sent successfully!")
        return True

    except Exception as e:
        print(f"Error sending email: {e}")
        return False

    finally:
        if 'server' in locals():
            server.quit()
            print("SMTP connection closed")

if __name__ == "__main__":
    test_email_sending()
