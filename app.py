"""
Astro Automation - Main Entry Point

This is the main entry point for the Astro Automation application.
It provides a simple interface to run the application in different modes.

Usage:
    python app.py [--monitor] [--interval SECONDS] [--debug] [--once]

Options:
    --monitor            Run in monitoring mode (continuous polling)
    --interval SECONDS   Polling interval in seconds (default: 180)
    --debug              Enable debug mode
    --once               Run once and exit (for testing)
"""

import os
import sys
import argparse


def setup_argument_parser():
    """Set up command line argument parser."""
    parser = argparse.ArgumentParser(description="Astro Automation")
    parser.add_argument(
        "--monitor",
        action="store_true",
        help="Run in monitoring mode (continuous polling)"
    )
    parser.add_argument(
        "--interval",
        type=int,
        default=180,
        help="Polling interval in seconds (default: 180)"
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug mode"
    )
    parser.add_argument(
        "--once",
        action="store_true",
        help="Run once and exit (for testing)"
    )
    return parser


def main():
    """Main function for the application."""
    # Parse command line arguments
    parser = setup_argument_parser()
    args = parser.parse_args()

    # Set debug mode if requested
    if args.debug:
        os.environ["ASTRO_DEBUG_MODE"] = "true"
        print("Debug mode enabled")

    # Run in monitoring mode if requested
    if args.monitor:
        print("Running in monitoring mode")
        from src.monitor import main as monitor_main
        monitor_args = []
        if args.interval != 180:
            monitor_args.extend(["--interval", str(args.interval)])
        if args.debug:
            monitor_args.append("--debug")
        if args.once:
            monitor_args.append("--once")
        
        # Replace sys.argv with our custom arguments
        sys.argv = [sys.argv[0]] + monitor_args
        monitor_main()
    else:
        # Run in standard mode (single execution)
        print("Running in standard mode (single execution)")
        from src.main import process_profiler, manage_log_files, profiler_controller
        
        # Manage log files
        manage_log_files()
        
        # Get the list of profilers to process
        profiler_list = profiler_controller.profiler_list
        
        # Process each profiler
        for profile in profiler_list:
            process_profiler(profile)


if __name__ == "__main__":
    main()
