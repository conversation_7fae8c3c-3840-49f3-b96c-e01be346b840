# Configuration Templates

This directory contains template files for the Astro Automation configuration. These templates provide examples and documentation for each configuration option.

## Available Templates

- `app.template.json`: Template for the main application configuration
- `profile.template.json`: Template for profile-specific configurations

## Configuration Structure

The Astro Automation configuration is organized into the following files:

- `app.json`: Main application settings
- `email.json`: Email server and template settings
- `logging.json`: Logging settings
- `api.json`: API connection settings
- `gdrive.json`: Google Drive settings
- `profiles/*.json`: Profile-specific configurations

## Environment Variables

The configuration files support environment variable substitution using the `${VARIABLE_NAME}` syntax. The following environment variables are used:

- `ASTRO_API_USERNAME`: API username for authentication
- `ASTRO_API_SECRET`: API secret for authentication
- `ASTRO_SMTP_EMAIL`: Email address for sending notifications
- `ASTRO_SMTP_PASSWORD`: Password or app password for the email account
- `ASTRO_EMAIL_RECIPIENT`: Default recipient for email notifications
- `ASTRO_GDRIVE_CLIENT_ID`: Google Drive client ID
- `ASTRO_GDRIVE_CLIENT_SECRET`: Google Drive client secret
- `ASTRO_GDRIVE_API_KEY`: Google Drive API key
- `GEMINI_API_KEY`: Gemini AI API key
- `ASTRO_DEBUG_MODE`: Enable or disable debug mode

## Configuration Options

### Application Settings (app.json)

- `Application.name`: Name of the application
- `Application.version`: Version of the application
- `Application.debug_mode`: Enable or disable debug mode
- `Application.polling_interval`: Default polling interval in seconds
- `Triggers.severity_levels`: List of severity levels to monitor
- `Triggers.trigger_email_condition`: Condition for triggering email notifications
- `Paths.templates_dir`: Directory for email templates
- `Paths.exports_dir`: Directory for exported files
- `Paths.hash_dir`: Directory for hash files
- `Service.polling_interval`: Polling interval for the service in seconds
- `Service.max_consecutive_errors`: Maximum number of consecutive errors before resetting
- `Service.error_wait_time`: Wait time in seconds after an error
- `Debug.debug_mode`: Enable or disable debug mode
- `Debug.log_level`: Log level for debug mode

### Profile Settings (profiles/*.json)

- `Profile.name`: Name of the profile
- `Profile.description`: Description of the profile
- `Email.sender`: Email address for sending notifications
- `Email.receivers`: List of email recipients
- `Email.subject`: Default email subject
- `Email.message`: Default email message
- `Email.use_template_in_body`: Use HTML template in email body
- `Email.attach_files`: Attach files to email
- `Email.use_gemini_ai`: Use Gemini AI for recommendations
- `API_URL.bearer_request_url`: URL for getting the bearer token
- `API_URL.api_request_url`: URL for making API requests
- `API_Credentials.api_username`: API username for authentication
- `API_Credentials.api_secret`: API secret for authentication
- `SMTP.server`: SMTP server address
- `SMTP.port`: SMTP server port
- `SMTP.email`: Email address for SMTP authentication
- `SMTP.app_pass`: Password or app password for SMTP authentication
- `Attachments.file1`: Path to the first attachment
- `Attachments.file2`: Path to the second attachment
- `Attachments.file3`: Path to the third attachment
- `Directory.directory`: Directory for exported files
- `Directory.hash_directory`: Directory for hash files
- `Docx.table_width_ratio`: Width ratio for table columns
- `Docx.table_total_width`: Total width of the table
- `Docx.document_title`: Title of the document
- `Docx.document_title_font`: Font for the document title
- `Docx.document_title_size`: Font size for the document title
- `Docx.content_font`: Font for the document content
- `Docx.content_size`: Font size for the document content
- `Triggers.send_docx`: Send DOCX files
- `Triggers.send_html`: Send HTML files
- `Triggers.send_xlsx`: Send XLSX files
- `Triggers.send_csv`: Send CSV files
- `Triggers.send_image`: Send image files
- `Triggers.send_email`: Send email notifications
- `Triggers.delete_local`: Delete local files after sending
- `Triggers.upload_gdrive`: Upload files to Google Drive
- `Triggers.trigger_email_condition`: Condition for triggering email notifications
- `Triggers.severity_levels`: List of severity levels to monitor
- `Email_Image.*`: Settings for generating email images
- `Google_Drive_API.*`: Settings for Google Drive integration

## Adding a New Profile

To add a new profile:

1. Copy the `profile.template.json` file to `profiles/your_profile_name.json`
2. Edit the file to customize the settings for your profile
3. Make sure to update the `Profile.name` and `Profile.description` fields

## Environment Variable Setup

Create a `.env` file in the root directory with the following content:

```
# API Credentials
ASTRO_API_USERNAME=your_api_username
ASTRO_API_SECRET=your_api_secret

# SMTP Email Settings
ASTRO_SMTP_EMAIL=<EMAIL>
ASTRO_SMTP_PASSWORD=your_app_password
ASTRO_EMAIL_RECIPIENT=<EMAIL>

# Google Drive API
ASTRO_GDRIVE_CLIENT_ID=your_gdrive_client_id
ASTRO_GDRIVE_CLIENT_SECRET=your_gdrive_client_secret
ASTRO_GDRIVE_API_KEY=your_gdrive_api_key

# Gemini AI API
GEMINI_API_KEY=your_gemini_api_key

# Debug Mode
ASTRO_DEBUG_MODE=false
```
