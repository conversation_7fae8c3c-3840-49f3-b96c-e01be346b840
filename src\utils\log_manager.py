"""
Log management utility for archiving and cleaning up old log files.
This module provides functions to archive old logs and clean up log directories.
"""
import os
import shutil
import datetime
import logging
from pathlib import Path
from typing import List, Optional

# Configure logging
logger = logging.getLogger(__name__)


def archive_old_logs(
    logs_dir: str = "logs",
    daily_dir: str = "daily",
    archive_dir: str = "archive",
    days_to_keep: int = 7
) -> List[str]:
    """
    Archive log files older than the specified number of days.

    Args:
        logs_dir: Base logs directory
        daily_dir: Directory containing daily logs
        archive_dir: Directory to store archived logs
        days_to_keep: Number of days to keep logs before archiving

    Returns:
        List of archived directories
    """
    try:
        # Calculate the cutoff date
        cutoff_date = datetime.datetime.now() - datetime.timedelta(days=days_to_keep)
        cutoff_str = cutoff_date.strftime("%Y-%m-%d")
        
        # Get the path to the daily logs directory
        daily_logs_path = os.path.join(logs_dir, daily_dir)
        archive_path = os.path.join(logs_dir, archive_dir)
        
        # Ensure archive directory exists
        os.makedirs(archive_path, exist_ok=True)
        
        # List of archived directories
        archived_dirs = []
        
        # Check if daily logs directory exists
        if not os.path.exists(daily_logs_path):
            logger.warning(f"Daily logs directory {daily_logs_path} does not exist")
            return archived_dirs
        
        # Iterate through date directories
        for date_dir in os.listdir(daily_logs_path):
            dir_path = os.path.join(daily_logs_path, date_dir)
            
            # Skip if not a directory or not a date format
            if not os.path.isdir(dir_path):
                continue
                
            # Check if the directory is older than the cutoff date
            if date_dir < cutoff_str:
                # Create archive directory for this date if it doesn't exist
                date_archive_path = os.path.join(archive_path, date_dir)
                os.makedirs(date_archive_path, exist_ok=True)
                
                # Move log files to archive
                for log_file in os.listdir(dir_path):
                    src_file = os.path.join(dir_path, log_file)
                    dst_file = os.path.join(date_archive_path, log_file)
                    
                    if os.path.isfile(src_file):
                        shutil.move(src_file, dst_file)
                        logger.info(f"Archived log file: {src_file} -> {dst_file}")
                
                # Remove the empty directory
                os.rmdir(dir_path)
                logger.info(f"Removed empty directory: {dir_path}")
                
                archived_dirs.append(date_dir)
        
        return archived_dirs
    
    except Exception as e:
        logger.error(f"Error archiving logs: {e}")
        return []


def cleanup_archive(
    logs_dir: str = "logs",
    archive_dir: str = "archive",
    max_archive_days: int = 30
) -> int:
    """
    Delete archived logs older than the specified number of days.

    Args:
        logs_dir: Base logs directory
        archive_dir: Directory containing archived logs
        max_archive_days: Maximum number of days to keep archived logs

    Returns:
        Number of deleted directories
    """
    try:
        # Calculate the cutoff date
        cutoff_date = datetime.datetime.now() - datetime.timedelta(days=max_archive_days)
        cutoff_str = cutoff_date.strftime("%Y-%m-%d")
        
        # Get the path to the archive directory
        archive_path = os.path.join(logs_dir, archive_dir)
        
        # Check if archive directory exists
        if not os.path.exists(archive_path):
            logger.warning(f"Archive directory {archive_path} does not exist")
            return 0
        
        # Counter for deleted directories
        deleted_count = 0
        
        # Iterate through date directories in the archive
        for date_dir in os.listdir(archive_path):
            dir_path = os.path.join(archive_path, date_dir)
            
            # Skip if not a directory or not a date format
            if not os.path.isdir(dir_path):
                continue
                
            # Check if the directory is older than the cutoff date
            if date_dir < cutoff_str:
                # Remove the directory and all its contents
                shutil.rmtree(dir_path)
                logger.info(f"Deleted archived logs: {dir_path}")
                deleted_count += 1
        
        return deleted_count
    
    except Exception as e:
        logger.error(f"Error cleaning up archive: {e}")
        return 0


def manage_logs(
    logs_dir: str = "logs",
    daily_dir: str = "daily",
    archive_dir: str = "archive",
    days_to_keep: int = 7,
    max_archive_days: int = 30
) -> dict:
    """
    Manage logs by archiving old logs and cleaning up the archive.

    Args:
        logs_dir: Base logs directory
        daily_dir: Directory containing daily logs
        archive_dir: Directory to store archived logs
        days_to_keep: Number of days to keep logs before archiving
        max_archive_days: Maximum number of days to keep archived logs

    Returns:
        Dictionary with results of the operation
    """
    # Archive old logs
    archived_dirs = archive_old_logs(logs_dir, daily_dir, archive_dir, days_to_keep)
    
    # Clean up archive
    deleted_count = cleanup_archive(logs_dir, archive_dir, max_archive_days)
    
    return {
        "archived_directories": archived_dirs,
        "deleted_directories": deleted_count
    }


if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    
    # Run log management
    result = manage_logs()
    
    print(f"Log management completed:")
    print(f"  - Archived directories: {len(result['archived_directories'])}")
    print(f"  - Deleted directories: {result['deleted_directories']}")
