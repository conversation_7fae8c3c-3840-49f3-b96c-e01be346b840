# Standard library imports
import os

# Third-party imports
from pydrive.auth import GoogleAuth
from pydrive.drive import GoogleDrive

# Project-specific imports
from src.utils.logger import logger


def upload_to_gdrive(profiler_name):
    """
    Upload files to Google Drive.

    Args:
        profiler_name: Profiler object containing Google Drive configuration
    """
    profiler = profiler_name
    logger.info("Starting Google Drive upload process")

    try:
        # Authenticate with Google Drive
        google_auth = GoogleAuth()
        google_drive = GoogleDrive(google_auth)
        logger.debug("Successfully authenticated with Google Drive")

        google_drive_folder = profiler.gdrive_directory
        logger.debug(f"Using Google Drive folder ID: {google_drive_folder}")

        # Upload files
        directory = profiler.directory

        # Check if directory exists
        if not os.path.exists(directory):
            logger.error(f"Directory {directory} does not exist")
            return

        # Get list of files to upload
        files = os.listdir(directory)
        logger.info(f"Found {len(files)} files to upload")

        # Upload each file
        for f in files:
            file_path = os.path.join(directory, f)
            if os.path.isfile(file_path):
                logger.info(f"Uploading file: {f}")
                gfile = google_drive.CreateFile({'parents': [{'id': google_drive_folder}], 'title': f})
                gfile.SetContentFile(file_path)
                gfile.Upload()
                logger.info(f"Successfully uploaded {f}")
                gfile = None

        logger.info("Google Drive upload process completed")
    except Exception as e:
        logger.error(f"Error uploading to Google Drive: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")



# def upload_to_gdrive():
#     api_key = profiler.gdrive_api_key  # Replace with your Google Drive API key
#     google_drive_folder = profiler.gdrive_directory
#     upload_url = 'https://www.googleapis.com/upload/drive/v3/files?uploadType=multipart&key=' + api_key

#     # Upload files
#     directory = profiler.directory

#     for filename in os.listdir(directory):
#         file_path = os.path.join(directory, filename)
#         file_metadata = {
#             'name': filename,
#             'parents': [google_drive_folder]
#         }

#         # Determine the file MIME type based on the file extension
#         mime_type = 'application/octet-stream'  # Default MIME type for unknown file types

#         if filename.endswith('.csv'):
#             mime_type = 'text/csv'
#         elif filename.endswith('.xlsx'):
#             mime_type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
#         elif filename.endswith('.docx'):
#             mime_type = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
#         elif filename.endswith('.png'):
#             mime_type = 'image/png'

#         # Create the multipart request payload
#         multipart_payload = {
#             'metadata': ('', json.dumps(file_metadata), 'application/json; charset=UTF-8'),
#             'file': (filename, open(file_path, 'rb'), mime_type)
#         }

#         # Send the multipart request to upload the file
#         response = requests.post(upload_url, files=multipart_payload)

#         if response.status_code == 200:
#             print(f'Uploaded file: {filename}')
#         else:
#             print(f'Failed to upload file: {filename} - {response.text}')
