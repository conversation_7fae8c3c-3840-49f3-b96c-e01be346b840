# Standard library imports
import os
import json
import base64
from typing import Dict, Any, Optional
import getpass

# Third-party imports
try:
    from cryptography.fernet import Fernet
    from cryptography.hazmat.primitives import hashes
    from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
    CRYPTOGRAPHY_AVAILABLE = True
except ImportError:
    CRYPTOGRAPHY_AVAILABLE = False


class CredentialManager:
    """
    Secure credential manager for handling sensitive information.

    This class provides methods to:
    1. Load credentials from environment variables
    2. Encrypt and decrypt sensitive information
    3. Store and retrieve credentials securely
    """

    def __init__(self, encryption_key_env_var: str = "ASTRO_ENCRYPTION_KEY"):
        """
        Initialize the credential manager.

        Args:
            encryption_key_env_var: Name of the environment variable containing the encryption key
        """
        self.encryption_key_env_var = encryption_key_env_var
        self.fernet = self._initialize_encryption()

    def _initialize_encryption(self) -> Optional[Fernet]:
        """
        Initialize the encryption system.

        Returns:
            Fernet object for encryption/decryption, or None if cryptography is not available
        """
        if not CRYPTOGRAPHY_AVAILABLE:
            print("Warning: cryptography package not available. Credentials will not be encrypted.")
            return None

        # Get encryption key from environment variable or prompt user
        encryption_key = os.environ.get(self.encryption_key_env_var)
        if not encryption_key:
            print(f"Environment variable {self.encryption_key_env_var} not found.")
            print("Please set this environment variable for secure credential storage.")
            print("For now, using a default key (NOT SECURE FOR PRODUCTION).")
            encryption_key = "default_key_not_secure_for_production"

        # Derive a key from the encryption key
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=b'astro_automation_salt',
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(encryption_key.encode()))
        return Fernet(key)

    def get_credential(self, credential_name: str, default: str = None) -> str:
        """
        Get a credential from environment variables.

        Args:
            credential_name: Name of the credential to retrieve
            default: Default value to return if credential is not found

        Returns:
            The credential value, or the default if not found
        """
        # Try to get from environment variable
        env_var_name = f"ASTRO_{credential_name.upper()}"
        credential = os.environ.get(env_var_name)

        if credential:
            # Check if the credential is empty
            if credential.strip():
                return credential
            else:
                print(f"Warning: Environment variable {env_var_name} is empty.")

        # If not found or empty and no default provided, prompt user
        if default is None or not default.strip():
            print(f"Credential {credential_name} not found in environment variables.")
            print(f"Please set the environment variable {env_var_name}.")
            print(f"You can run setup_credentials.py to set up all credentials.")

            # In interactive mode, prompt for input
            try:
                credential = getpass.getpass(f"Enter {credential_name} (input will be hidden): ")
                if credential.strip():
                    # Store the credential in environment variable for this session
                    os.environ[env_var_name] = credential
                    return credential
                else:
                    print(f"Warning: Empty credential provided for {credential_name}.")
                    return ""
            except (EOFError, KeyboardInterrupt):
                # Non-interactive mode
                print(f"Error: Cannot prompt for {credential_name} in non-interactive mode.")
                return default or ""

        return default

    def encrypt_data(self, data: str) -> Optional[str]:
        """
        Encrypt sensitive data.

        Args:
            data: Data to encrypt

        Returns:
            Encrypted data as a string, or None if encryption is not available
        """
        if not self.fernet:
            return data

        encrypted_data = self.fernet.encrypt(data.encode())
        return base64.urlsafe_b64encode(encrypted_data).decode()

    def decrypt_data(self, encrypted_data: str) -> Optional[str]:
        """
        Decrypt sensitive data.

        Args:
            encrypted_data: Encrypted data to decrypt

        Returns:
            Decrypted data as a string, or None if decryption is not available
        """
        if not self.fernet:
            return encrypted_data

        try:
            decoded_data = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted_data = self.fernet.decrypt(decoded_data)
            return decrypted_data.decode()
        except Exception as e:
            print(f"Error decrypting data: {e}")
            return encrypted_data

    def update_config_with_env_vars(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update configuration data with values from environment variables.

        Args:
            config_data: Configuration data to update

        Returns:
            Updated configuration data
        """
        import re

        # Helper function to check if a value contains an environment variable placeholder
        def contains_env_var(value):
            if isinstance(value, str):
                return bool(re.search(r'\${([A-Za-z0-9_]+)}', value))
            return False

        # Helper function to get the actual value, replacing placeholders if needed
        def get_actual_value(env_var, config_value):
            # If the config value contains a placeholder, we need to replace it
            if contains_env_var(config_value):
                # Extract the environment variable name from the placeholder
                match = re.search(r'\${([A-Za-z0-9_]+)}', config_value)
                if match:
                    # Use the extracted name to get the value from environment
                    return self.get_credential(env_var, "")

            # Otherwise, use the config value as the default
            return self.get_credential(env_var, config_value)

        # Update API credentials
        if "API_Credentials" in config_data:
            config_data["API_Credentials"]["api_username"] = get_actual_value(
                "API_USERNAME",
                config_data["API_Credentials"]["api_username"]
            )
            config_data["API_Credentials"]["api_secret"] = get_actual_value(
                "API_SECRET",
                config_data["API_Credentials"]["api_secret"]
            )

        # Update SMTP credentials
        if "SMTP" in config_data:
            smtp_email = get_actual_value(
                "SMTP_EMAIL",
                config_data["SMTP"]["email"]
            )
            config_data["SMTP"]["email"] = smtp_email
            config_data["SMTP"]["app_pass"] = get_actual_value(
                "SMTP_PASSWORD",
                config_data["SMTP"]["app_pass"]
            )

            # Also update the Email sender to match SMTP email if Email section exists
            if "Email" in config_data and smtp_email:
                # Only update if sender is empty or matches the old SMTP email or contains a placeholder
                if (not config_data["Email"]["sender"] or
                    config_data["Email"]["sender"] == config_data["SMTP"]["email"] or
                    contains_env_var(config_data["Email"]["sender"])):
                    config_data["Email"]["sender"] = smtp_email

        # Update Email recipient
        if "Email" in config_data and "receivers" in config_data["Email"]:
            # Handle both string and list receivers
            if isinstance(config_data["Email"]["receivers"], list):
                for i, receiver in enumerate(config_data["Email"]["receivers"]):
                    if contains_env_var(receiver):
                        config_data["Email"]["receivers"][i] = get_actual_value(
                            "EMAIL_RECIPIENT",
                            receiver
                        )
            elif isinstance(config_data["Email"]["receivers"], str):
                if contains_env_var(config_data["Email"]["receivers"]):
                    config_data["Email"]["receivers"] = get_actual_value(
                        "EMAIL_RECIPIENT",
                        config_data["Email"]["receivers"]
                    )

        # Update Email sender separately if needed
        if "Email" in config_data:
            # Only get from environment if not already set from SMTP email
            if not config_data["Email"]["sender"] or contains_env_var(config_data["Email"]["sender"]):
                config_data["Email"]["sender"] = get_actual_value(
                    "EMAIL_SENDER",
                    config_data["Email"]["sender"]
                )

        # Update Google Drive API credentials
        if "Google_Drive_API" in config_data:
            config_data["Google_Drive_API"]["gdrive_client_id"] = get_actual_value(
                "GDRIVE_CLIENT_ID",
                config_data["Google_Drive_API"]["gdrive_client_id"]
            )
            config_data["Google_Drive_API"]["gdrive_client_secret"] = get_actual_value(
                "GDRIVE_CLIENT_SECRET",
                config_data["Google_Drive_API"]["gdrive_client_secret"]
            )
            config_data["Google_Drive_API"]["gdrive_api_key"] = get_actual_value(
                "GDRIVE_API_KEY",
                config_data["Google_Drive_API"]["gdrive_api_key"]
            )

        return config_data


# Create a singleton instance of the credential manager
credential_manager = CredentialManager()


def _process_env_vars_in_config(config_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Process environment variables in configuration values.

    Args:
        config_data: Configuration data to process

    Returns:
        Processed configuration data
    """
    import re

    def replace_env_vars(value):
        if isinstance(value, str):
            # Find all environment variables in the string
            pattern = r'\${([A-Za-z0-9_]+)}'
            matches = re.findall(pattern, value)

            # If no environment variables found, return the value as is
            if not matches:
                return value

            # Replace each environment variable with its value
            result = value
            for match in matches:
                # Try with the exact name first
                env_value = os.environ.get(match)

                # If not found, try with ASTRO_ prefix
                if env_value is None and not match.startswith('ASTRO_'):
                    env_value = os.environ.get(f'ASTRO_{match}')

                if env_value is not None:
                    result = result.replace(f'${{{match}}}', env_value)
                else:
                    # If environment variable not found, use the actual value from the config
                    # This is important for the update_config_with_env_vars function to work properly
                    print(f"Warning: Environment variable ${{{match}}} not found")
                    # Don't replace the placeholder, let update_config_with_env_vars handle it

            return result
        elif isinstance(value, dict):
            return {k: replace_env_vars(v) for k, v in value.items()}
        elif isinstance(value, list):
            return [replace_env_vars(item) for item in value]
        else:
            return value

    return replace_env_vars(config_data)


def load_config_with_credentials(config_file_path: str) -> Dict[str, Any]:
    """
    Load configuration from a file and update it with credentials from environment variables.

    Args:
        config_file_path: Path to the configuration file

    Returns:
        Configuration data with updated credentials
    """
    with open(config_file_path, 'r') as f:
        config_data = json.load(f)

    # Process environment variables in the config
    config_data = _process_env_vars_in_config(config_data)

    # Update config with credentials from environment variables
    config_data = credential_manager.update_config_with_env_vars(config_data)

    return config_data
