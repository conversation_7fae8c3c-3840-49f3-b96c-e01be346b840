# Standard library imports
import os
import ssl
import smtplib
import base64
from smtplib import SMTPException
from datetime import datetime
from email import encoders
from email.mime.base import MIMEBase
from email.mime.image import MIMEImage
from email.mime.multipart import <PERSON><PERSON><PERSON><PERSON>ip<PERSON>
from email.mime.text import MIMEText
import traceback

# Third-party imports
from jinja2 import Environment, FileSystemLoader
from PIL import Image, ImageDraw, ImageFont

# Project-specific imports
from src.logic import api_method
from src.logic import recommendation
from src.utils.logger import logger

# Import Gmail OAuth2 module if available
try:
    from src.utils.gmail_oauth import get_oauth2_string
    OAUTH2_AVAILABLE = True
except ImportError:
    OAUTH2_AVAILABLE = False
    logger.warning("Gmail OAuth2 module not available. Using password authentication.")


def get_alarm_data_for_email(profiler):
    """
    Get alarm data for email from the profiler or API.

    Args:
        profiler: The profiler object containing configuration

    Returns:
        List of alarm data dictionaries or None if no data could be retrieved
    """
    # Try to get data using the most reliable method first
    if hasattr(profiler, 'get_data'):
        logger.debug("Getting data from profiler.get_data()")
        data = profiler.get_data()
        if data:
            return data

    # If that fails, try the data attribute
    if hasattr(profiler, 'data'):
        logger.debug("Getting data from profiler.data attribute")
        data = profiler.data
        if data:
            return data

    # As a last resort, get data directly from the API
    if hasattr(profiler, 'bearer_req_url') and hasattr(profiler, 'api_request_url'):
        logger.debug("Getting data directly from API")

        # Use the configured severity levels
        severity_levels = getattr(profiler, 'severity_levels', ['medium', 'high'])
        logger.debug(f"Using severity levels: {severity_levels}")

        # Get data directly from API
        try:
            data = api_method.api_filter(
                profiler.bearer_req_url,
                profiler.api_request_url,
                severity_levels=severity_levels
            )
            if data:
                return data
        except Exception as e:
            logger.error(f"Error getting data from API: {e}")
            logger.debug(traceback.format_exc())

    # If we get here, no data could be retrieved
    return None


def update_email_subject(msg, data, default_subject):
    """
    Update the email subject with the most recent alarm subject if available.

    Args:
        msg: The email message object
        data: The alarm data
        default_subject: The default subject to use if no alarm subject is found

    Returns:
        The updated message object
    """
    # Use the provided subject initially
    msg["Subject"] = default_subject

    # Update the email subject with the most recent alarm subject if available
    if data and len(data) > 0:
        # Find the most recent alarm with an Email Subject
        most_recent_alarm = None
        for alarm in data:
            if isinstance(alarm, dict) and "Email Subject" in alarm:
                most_recent_alarm = alarm

        # Use the subject from the most recent alarm
        if most_recent_alarm and "Email Subject" in most_recent_alarm:
            custom_subject = most_recent_alarm["Email Subject"]
            logger.info(f"Using subject from most recent alarm: {custom_subject}")
            # Update the email subject - completely replace it
            if "Subject" in msg:
                del msg["Subject"]
            msg["Subject"] = custom_subject

    return msg


def validate_alarm_data(data):
    """
    Validate alarm data to ensure it has the required fields.

    Args:
        data: The alarm data to validate

    Returns:
        List of valid alarm data dictionaries
    """
    valid_data = []

    if not data:
        return valid_data

    for item in data:
        # Check if this is a properly structured alarm with required fields
        if isinstance(item, dict) and 'Severity' in item and 'Event ID' in item:
            valid_data.append(item)
        else:
            logger.debug(f"Skipping invalid alarm data: {item}")

    return valid_data


def add_attachments_to_email(msg, profiler):
    """
    Add file attachments to the email if configured.

    Args:
        msg: The email message object
        profiler: The profiler object containing configuration

    Returns:
        The updated message object
    """
    # Check if we should attach files
    attach_files = False
    if hasattr(profiler, 'config') and isinstance(profiler.config, dict):
        attach_files = profiler.config.get("Email", {}).get("attach_files", False)

    if not attach_files:
        return msg

    # Check if directory exists
    if not os.path.exists(profiler.directory):
        logger.warning(f"Attachment directory '{profiler.directory}' does not exist")
        return msg

    # Attach files from the directory
    for attachment_path in os.listdir(profiler.directory):
        path = os.path.join(profiler.directory, attachment_path)
        if os.path.isfile(path):  # Make sure it's a file
            logger.info(f"Attaching file '{attachment_path}'")

            try:
                with open(path, "rb") as attachment:
                    part = MIMEBase("application", "octet-stream")
                    part.set_payload(attachment.read())
                    encoders.encode_base64(part)
                    part.add_header("Content-Disposition", f"attachment; filename={attachment_path}")
                    msg.attach(part)
            except Exception as e:
                logger.error(f"Error attaching file {path}: {e}")
                logger.debug(traceback.format_exc())

    return msg


def add_image_to_email(msg, profiler):
    """
    Add a custom image to the email if configured.

    Args:
        msg: The email message object
        profiler: The profiler object containing configuration

    Returns:
        The updated message object
    """
    if not profiler.send_image:
        return msg

    try:
        # Create the custom image
        create_custom_image(profiler)

        # Read the image file for embedding in the email
        with open(profiler.image_output_path, "rb") as image_file:
            image_data = image_file.read()

        # Add HTML to display the image
        html = f"""
            <html>
            <body>
                <img src="cid:image_attachment" alt="Custom Image">
            </body>
            </html>
            """
        msg.attach(MIMEText(html, "html"))

        # Attach the image with a content ID for referencing in the HTML
        with open(profiler.image_output_path, "rb") as image_file:
            image_data = image_file.read()
            image_mime = MIMEImage(image_data)
            image_mime.add_header("Content-ID", "<image_attachment>")
            msg.attach(image_mime)

        logger.info(f"Added image {profiler.image_output_path} to email")
    except Exception as e:
        logger.error(f"Error adding image to email: {e}")
        logger.debug(traceback.format_exc())

    return msg


def send_query_to_email(sender, recipient, gmail_app_pass, message, smtp_server, smtp_port, email_subject,
                        profiler_name):
    """
    Send an email with alarm information.

    Args:
        sender: Email sender address
        recipient: List of email recipients
        gmail_app_pass: Gmail app password for authentication
        message: Email message body
        smtp_server: SMTP server address
        smtp_port: SMTP server port
        email_subject: Default email subject (may be overridden by alarm data)
        profiler_name: Profiler object containing configuration

    Returns:
        bool: True if the email was sent successfully, False otherwise
    """
    context = ssl.create_default_context()
    profiler = profiler_name
    server = None

    # Debug logging
    logger.info(f"Starting email sending process")
    logger.info(f"Email sender: {sender}")
    logger.info(f"Email recipients: {recipient}")
    logger.info(f"SMTP server: {smtp_server}:{smtp_port}")
    logger.info(f"Email subject: {email_subject}")

    try:
        # Connect to SMTP server
        logger.info("Connecting to SMTP server...")
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls(context=context)

        # Check if we should use OAuth2 authentication
        use_oauth2 = False
        if OAUTH2_AVAILABLE and smtp_server == "smtp.gmail.com":
            # Check if we have a configuration for OAuth2
            if hasattr(profiler, 'config') and isinstance(profiler.config, dict):
                use_oauth2 = profiler.config.get("SMTP", {}).get("use_oauth2", False)
                logger.info(f"OAuth2 configuration from profile: {use_oauth2}")

            # Do not override the configuration
            # Removed the automatic fallback to OAuth2

        # Authenticate with the SMTP server
        logger.info("Logging into SMTP server...")

        # For Gmail, ensure the app password is properly formatted (no spaces)
        if smtp_server == "smtp.gmail.com":
            # Remove any spaces from the app password
            gmail_app_pass = gmail_app_pass.replace(" ", "")
            logger.info("Formatted Gmail app password (removed spaces)")

        if use_oauth2:
            try:
                # Get the OAuth2 string
                logger.info("Using OAuth2 authentication for Gmail")
                auth_string = get_oauth2_string(sender)
                if auth_string:
                    # Use XOAUTH2 authentication
                    server.auth("XOAUTH2", lambda x: auth_string)
                    logger.info("Successfully authenticated with OAuth2")
                else:
                    # Fall back to password authentication
                    logger.warning("Failed to get OAuth2 string, falling back to password authentication")
                    server.login(sender, gmail_app_pass)
            except Exception as e:
                logger.error(f"OAuth2 authentication failed: {e}")
                logger.info("Falling back to password authentication")
                server.login(sender, gmail_app_pass)
        else:
            # Use regular password authentication
            logger.info(f"Using password authentication for {sender}")
            try:
                server.login(sender, gmail_app_pass)
                logger.info("Successfully logged in with password")
            except Exception as e:
                logger.error(f"Password authentication failed: {e}")
                # Try with original password from environment as a fallback
                original_password = os.environ.get("ASTRO_SMTP_PASSWORD", "")
                if original_password and original_password != gmail_app_pass:
                    logger.info("Trying with original password from environment")
                    try:
                        server.login(sender, original_password)
                    except Exception as e2:
                        logger.error(f"Fallback authentication also failed: {e2}")
                        raise

        # Create email message
        msg = MIMEMultipart('alternative')  # Use 'alternative' to support both plain text and HTML
        msg["From"] = sender

        # Handle recipient formatting - ensure it's a list of separate email addresses
        if isinstance(recipient, list):
            if len(recipient) == 1 and ',' in recipient[0]:
                # Split the comma-separated string into a list of email addresses
                recipient = recipient[0].split(',')
                logger.info(f"Split recipient string into list: {recipient}")

        msg["To"] = ", ".join(recipient)

        # Get alarm data
        logger.info("Retrieving alarm data for email...")
        data = get_alarm_data_for_email(profiler)

        # Log the results
        if data:
            logger.info(f"Successfully retrieved {len(data)} alarms")
        else:
            logger.warning("No alarm data retrieved")
            return False

        # Update email subject based on alarm data
        msg = update_email_subject(msg, data, email_subject)

        # Check if we have valid data before proceeding
        if not data or len(data) == 0:
            logger.warning("No valid alarm data available. Aborting email send.")
            return False

        # Add plain text part
        plain_text = message if message else "Please see the information below."
        msg.attach(MIMEText(plain_text, "plain"))

        # Check if we should use the template in the body
        use_template_in_body = True
        if hasattr(profiler, 'config') and isinstance(profiler.config, dict):
            use_template_in_body = profiler.config.get("Email", {}).get("use_template_in_body", True)

        # If we have data and should use template in body, create HTML email body
        if data and use_template_in_body:
            logger.info(f"Creating HTML email body with {len(data)} alarms")
            try:
                # Validate data structure before creating HTML
                valid_data = validate_alarm_data(data)

                if not valid_data:
                    logger.warning("No valid alarms found in data. Aborting email send.")
                    return False

                # Create HTML body
                html_body = create_html_email_body(valid_data, profiler)

                # Make sure the HTML part is the primary part (will be displayed by default)
                # Remove any existing plain text part
                for part in msg.get_payload():
                    if part.get_content_type() == "text/plain":
                        msg.get_payload().remove(part)
                        break

                # Add the HTML part as the primary part
                msg.attach(MIMEText(html_body, "html"))

                # Save the HTML to a file for debugging (only in debug mode)
                if os.environ.get("ASTRO_DEBUG_MODE", "false").lower() in ("true", "1", "yes", "y"):
                    debug_html_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "debug_email.html")
                    with open(debug_html_path, "w") as f:
                        f.write(html_body)
                    logger.debug(f"Saved HTML content to {debug_html_path}")

                logger.info("Added HTML content to email body")
            except Exception as e:
                logger.error(f"Error creating HTML email body: {e}")
                logger.error(f"Traceback: {traceback.format_exc()}")
                # Fall back to plain text
                msg.attach(MIMEText(f"Error creating HTML email. Please see the {len(data)} alarms below:\n\n{str(data)}", "plain"))
        else:
            logger.debug(f"Not creating HTML email body. Data exists: {data is not None}, Use template: {use_template_in_body}")
            # Add a more informative message if we have data but aren't using the template
            if data and not use_template_in_body:
                msg.attach(MIMEText(f"Found {len(data)} alarms but template is disabled. Raw data: {str(data)}", "plain"))

        # Add attachments if configured
        msg = add_attachments_to_email(msg, profiler)

        # Add image if configured
        msg = add_image_to_email(msg, profiler)

        # Send the email
        logger.info("Sending email...")
        server.sendmail(sender, recipient, msg.as_string())

        logger.info("Email sent successfully")
        return True

    except SMTPException as smtp_error:
        logger.error(f"SMTP Error occurred while sending the email: {smtp_error}")
        return False
    except Exception as e:
        logger.error(f"An error has occurred with the following details: {e}")
        logger.error(f"Exception type: {type(e).__name__}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False
    finally:
        if server:
            try:
                server.quit()
                logger.info("SMTP connection closed")
            except Exception as e:
                logger.debug(f"Error closing SMTP connection: {e}")
                pass


def create_html_email_body(data, profiler):
    """
    Create an HTML email body using the Jinja2 template.

    Args:
        data: List of dictionaries containing the alarm data
        profiler: Profiler object containing configuration

    Returns:
        str: HTML email body
    """
    # Check if data is empty and log a warning
    if not data or len(data) == 0:
        logger.warning("No data available for email template")
        return ""

    # Get the project root directory (3 levels up from this file)
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))

    # Try multiple possible template directories in order of preference
    template_dirs = [
        # New structure - resources/templates
        os.path.join(project_root, "resources", "templates"),
        # Old structure - src/resources/templates
        os.path.join(project_root, "src", "resources", "templates"),
        # Fallback - templates in the current directory
        os.path.join(os.path.dirname(__file__), "templates")
    ]

    # Find the first template directory that exists
    template_dir = None
    for dir_path in template_dirs:
        if os.path.exists(dir_path):
            template_dir = dir_path
            logger.info(f"Using template directory: {template_dir}")
            break

    if not template_dir:
        logger.error("Could not find any template directory")
        logger.error(f"Tried: {', '.join(template_dirs)}")
        return ""

    # Configure Jinja2 template environment
    env = Environment(loader=FileSystemLoader(template_dir))

    # Check for template files in order of preference
    template_files = ["email_template.html", "template.html"]
    template_file = None

    for file_name in template_files:
        template_path = os.path.join(template_dir, file_name)
        if os.path.exists(template_path):
            template_file = file_name
            logger.info(f"Using template file: {template_file}")
            break

    if not template_file:
        # List available templates for debugging
        try:
            available_templates = os.listdir(template_dir)
            logger.debug(f"Available templates: {available_templates}")
        except Exception as e:
            logger.error(f"Error listing templates: {e}")
            available_templates = []

        logger.error("No suitable template found. Cannot create HTML email.")
        return ""

    # Get the template
    template = env.get_template(template_file)

    # Get the current year for the footer
    current_year = datetime.now().year

    # Log template variables at debug level
    logger.debug(f"Template title: {profiler.document_title}")
    logger.debug(f"Template alarms count: {len(data)}")
    logger.debug(f"Template current year: {current_year}")

    # Only log sample data in debug mode
    if os.environ.get("ASTRO_DEBUG_MODE", "false").lower() in ("true", "1", "yes", "y"):
        logger.debug(f"Sample data item: {data[0] if data else 'None'}")

    # Get a batch recommendation from Gemini AI for all alarms
    gemini_recommendation = None
    try:
        # Check if we should use Gemini AI
        use_gemini = True
        if hasattr(profiler, 'config') and isinstance(profiler.config, dict):
            use_gemini = profiler.config.get("Email", {}).get("use_gemini_ai", True)

        if use_gemini:
            logger.info("Getting batch recommendation from Gemini AI")
            gemini_recommendation = recommendation.get_batch_gemini_recommendation(data)

            if gemini_recommendation:
                logger.info("Successfully received recommendation from Gemini AI")

                # Update all alarms with the same Gemini recommendation
                # Clean up the recommendation by removing markdown code block markers
                cleaned_recommendation = gemini_recommendation.replace("```html", "").replace("```", "").strip()
                for alarm in data:
                    if isinstance(alarm, dict):
                        alarm["Recommendation"] = cleaned_recommendation
            else:
                logger.warning("Failed to get recommendation from Gemini AI, using default recommendations")
    except Exception as e:
        logger.error(f"Error getting Gemini AI recommendation: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")

    # Prepare template variables - we use the same variable structure for both templates
    template_vars = {
        "title": profiler.document_title,
        "alarms": data,  # email_template.html uses 'alarms'
        "data": data,    # template.html uses 'data'
        "current_year": current_year
        # Removed gemini_recommendation as it's now included in each alarm
    }

    # Render the template with data
    html_content = template.render(**template_vars)

    logger.info(f"Created HTML email body using {template_file}")
    return html_content


def create_custom_image(profiler_name):
    """
    Create a custom image with severity counts for email embedding.

    Args:
        profiler_name: The profiler object containing image configuration
    """
    profiler = profiler_name
    logger.info("Creating custom image for email")

    try:
        # Create a new image with the specified width, height, and background color
        image = Image.new("RGB", (profiler.image_width, profiler.image_height), profiler.image_background_color)

        # Create a drawing context
        draw = ImageDraw.Draw(image)

        # Specify the font properties
        title_font = ImageFont.truetype(profiler.image_font, profiler.image_font_size + 10)
        text_font = ImageFont.truetype(profiler.image_font, profiler.image_font_size)
        footer_font = ImageFont.truetype(profiler.image_font, profiler.image_font_size - 2)

        # Calculate the position of the title
        title_width, title_height = draw.textsize(profiler.image_title, font=title_font)
        title_position = ((profiler.image_width - title_width) // 2, 20)

        # Draw the title on the image
        draw.text(title_position, profiler.image_title, font=title_font, fill=profiler.image_title_color)

        # Calculate the position of the text lines
        text_position = (50, title_position[1] + title_height + 20)

        # Get severity counts using the configurable severity levels
        severity_levels = getattr(profiler, "severity_levels", ["low", "medium", "high"])
        logger.debug(f"Getting severity counts for levels: {severity_levels}")

        val = api_method.count_severity(severity_levels=severity_levels)
        logger.debug(f"Severity counts: {val}")

        # Draw the text lines on the image
        for index, line in enumerate(profiler.image_text_lines):
            # Make sure we don't go out of bounds
            if index < len(val):
                draw.text(text_position, line + str(val[index]), font=text_font, fill=profiler.image_text_color)
                text_position = (text_position[0], text_position[1] + profiler.image_font_size + 10)

        # Calculate the position of the footer
        footer_width, footer_height = draw.textsize(profiler.image_footer, font=footer_font)
        footer_position = ((profiler.image_width - footer_width) // 2, profiler.image_height - footer_height - 20)

        # Draw the footer on the image
        draw.text(footer_position, profiler.image_footer, font=footer_font, fill=profiler.image_footer_color)

        # Save the image to the specified output path
        image.save(profiler.image_output_path)
        logger.info(f"Created image file at {profiler.image_output_path}")

    except Exception as e:
        logger.error(f"Error creating custom image: {e}")
        logger.error(traceback.format_exc())
